import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>t, Eye, EyeSlash } from "iconsax-react";
import { useNavigate } from "react-router-dom";

interface AuthenticateProps {
  onSuccess?: () => void;
}

export const Authenticate: React.FC<AuthenticateProps> = ({ onSuccess }) => {
  const [pin, setPin] = useState(["", "", "", ""]);
  const [showPin, setShowPin] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const navigate = useNavigate();

  const handlePinChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newPin = [...pin];
    newPin[index] = value;
    setPin(newPin);

    // Auto focus next input
    if (value && index < 3) {
      const nextInput = document.getElementById(`auth-pin-${index + 1}`);
      nextInput?.focus();
    }
  };

  const isPinComplete = pin.join("").length === 4;

  const handleContinue = () => {
    if (isPinComplete) {
      setShowSuccessModal(true);
    }
  };

  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    if (onSuccess) {
      onSuccess();
    } else {
      navigate(-1); // Go back to the previous page (gift registry details)
    }
  };

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto px-4 pt-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft size="20" color="#666" />
            </button>
            <span className="ml-4 text-gray-600">Back to Withdrawal</span>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8 px-2">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
                ✓
              </div>
              <span className="ml-2 text-xs text-green-600 font-medium">
                Create Transaction Pin
              </span>
            </div>
            <div className="flex-1 h-px bg-gray-300 mx-3"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
                ✓
              </div>
              <span className="ml-2 text-xs text-green-600 font-medium">
                Select Account
              </span>
            </div>
            <div className="flex-1 h-px bg-gray-300 mx-3"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-medium">
                3
              </div>
              <span className="ml-2 text-xs text-primary font-medium">
                Authenticate
              </span>
            </div>
          </div>

          {/* Content */}
          <div className="text-center">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              Withdraw from Wallet
            </h1>
            <p className="text-gray-600 mb-8">
              Enter your 4-digit transaction pin to continue
            </p>

            {/* PIN Input */}
            <div className="mb-6">
              <div className="flex justify-center gap-3 mb-6">
                {pin.map((digit, index) => (
                  <input
                    key={index}
                    id={`auth-pin-${index}`}
                    type={showPin ? "text" : "password"}
                    value={digit}
                    onChange={(e) => handlePinChange(index, e.target.value)}
                    className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
                    maxLength={1}
                  />
                ))}
              </div>

              <button
                onClick={() => setShowPin(!showPin)}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mx-auto mb-8"
              >
                {showPin ? <EyeSlash size="16" /> : <Eye size="16" />}
                <span className="text-sm">{showPin ? "Hide" : "Show"} PIN</span>
              </button>
            </div>

            <button
              onClick={handleContinue}
              disabled={!isPinComplete}
              className={`w-full py-3 px-4 rounded-full font-medium ${
                isPinComplete
                  ? "bg-primary text-white hover:bg-primary-600"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              Continue
            </button>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl p-8 text-center shadow-xl max-w-md w-full">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="text-2xl sm:text-4xl">🎉</div>
            </div>

            {/* Content */}
            <h2 className="text-lg md:text-2xl font-semibold text-gray-900 mb-2">
              Your Transaction pin has
            </h2>
            <p className="text-base sm:text-lg text-gray-600 mb-6">
              been set successfully
            </p>

            <p className="text-gray-600 mb-4 sm:text-base text-[12px] sm:mb-8">
              You're all set. You can now safely withdraw your funds into your
              account.
            </p>

            {/* Continue Button */}
            <button
              onClick={handleSuccessClose}
              className="bg-primary text-white py-3 px-8 rounded-full font-medium hover:bg-primary-600 transition-colors"
            >
              Continue
            </button>
          </div>
        </div>
      )}
    </>
  );
};
