import { SVGProps } from "react";
import Icons from "../../assets/icons/custom-icon-library";
export interface IconPropTypes extends SVGProps<SVGSVGElement> {
  color?: string;
  secondaryColor?: string;
  width?: number;
  height?: number;
}

export function Icon({
  name,
  color,
  secondaryColor,
  ...props
}: IconPropTypes & { name: keyof typeof Icons }) {
  const Icon = Icons[name];
  
  // Pass color props to the icon function if it's the giftItems icon
  const exportedIcon = name === 'giftItems' 
    ? <Icon color={color} secondaryColor={secondaryColor} {...props} /> 
    : <Icon {...props} />;
    
  return exportedIcon;
}
