import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Footer } from "../footer";
import { ArrowRight } from "iconsax-react";
import wed from "../../../assets/images/wed.png";
export const CreatedSuccess = ({ onClose }: { onClose: () => void }) => {
  const navigate = useNavigate();
  const guestlist = () => {
    onClose();
    navigate("/guest-lists");
  };
  const goHome = () => {
    onClose();
    navigate("/");
  };
  return (
    <div>
      <div className="relative w-full pt-20 md:pt-34 px-4 md:px-0 pb-56">
        <div
          className="absolute inset-0 h-[720px] md:h-[774px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-0"
          style={{ backgroundSize: "cover" }}
        />
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-20 bg-white border-t border-white rounded-[20px] text-center max-w-[450px] w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]"
        >
          {/* Triangle background */}
          <div
            className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[262px] w-full overflow-hidden"
            style={{
              clipPath:
                "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
            }}
          />

          {/* Image container */}
          <div className="absolute top-10 sm:top-0 left-0  overflow-hidden">
            <img
              src={wed}
              alt="Wedding celebration"
              className="w-full h-full object-cover"
            />
          </div>

          <div className="flex flex-col items-center text-center py-12 px-4 w-full">
            <h2 className="text-2xl md:text-4xl font-medium my-2 text-dark-200">
              Your Guestlist has <br />{" "}
              <span className="text-[18px] md:text-[26px] text-grey-250">
                been created Successfully
              </span>
            </h2>
            <p className="text-grey-250 text-base mt-4 mb-7.5">
              Invites sent successfully! Your guests <br />
              will receive them shortly.
            </p>
            <button
              type="button"
              onClick={guestlist}
              className="bg-primary cursor-pointer text-2xl text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors"
            >
              <span>Go To Guestlist</span>
              <div className="rounded-full bg-white/30 p-0.5">
                <ArrowRight size="16" color="#fff" />
              </div>
            </button>
          </div>
        </motion.div>
        <div className="flex justify-center mt-10">
          <button
            type="button"
            onClick={goHome}
            className="underline text-cus-orange-100 italic font-bold text-lg cursor-pointer"
          >
            Back to dashboard
          </button>
        </div>{" "}
      </div>
      <Footer />
    </div>
  );
};
