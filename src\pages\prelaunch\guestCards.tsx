import { CloseCircle } from 'iconsax-react';
import { useState } from 'react';
import { modalVariants } from '../../components/reuseables/animations/animations';
import { motion } from 'motion/react';

const contacts = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 4,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 5,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: 6,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
];

const Card = ({
  name,
  email,
  onClose,
  index,

}: {
  name: string;
  email: string;
  onClose: () => void;
  index: number;
}) => {
  const opacityValues = [1, 0.8, 0.64, 0.45, 0.24, 0.16];
  const opacity = index < opacityValues.length ? opacityValues[index] : 0.16;

  return (
    <div
      className="w-full rounded-2xl mb-3 bg-white border border-gray-100 py-4  pl-5 pr-2.5 flex justify-between items-start"
      style={{ opacity }}>

      <div className="flex flex-col ">
        <h3 className="text-dark-blue-100 font-semibold text-sm">{name}</h3>
        <p className="text-grey-650 text-xs">{email}</p>
      </div>
      <button onClick={onClose} className="cursor-pointer">
        <CloseCircle size="20" color="#9499F7" variant="Bulk" />
      </button>
    </div>
  );
};

export const GuestCards = () => {
  const [visibleContacts, setVisibleContacts] = useState(contacts);

  const removeContact = (id: number) => {
    setVisibleContacts(visibleContacts.filter((contact) => contact.id !== id));
  };

  return (
    <div className="mb-10 md:mb-0 overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none mx-4 md:mx-0 px-4 pt-5 md:pt-20 h-full bg-gradient-to-br from-[#F5F9FF] via-[#F5F6FE] to-[#FEFAF8] via-47.6% from-27.46% to-89.34%">

      <motion.div
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={modalVariants}
        className="h-[485px] overflow-hidden">
        {visibleContacts.map((contact, index) => (

          <Card
            key={contact.id}
            name={contact.name}
            email={contact.email}
            onClose={() => removeContact(contact.id)}
            index={index}

          />
        ))}

        {visibleContacts.length === 0 && (
          <div className="text-center py-6 text-gray-500">
            No contacts available
          </div>
        )}
      </motion.div>
    </div>
  );
};
