import { ArrowRight, CloseCircle } from 'iconsax-react';
import { <PERSON>actN<PERSON>, MouseEventHandler, useEffect } from 'react';
import { Button } from '../button/onboardingButton';
import { motion, AnimatePresence } from 'motion/react';
import {
  modalVariants,
  textSlideInVariants,
  textVariants,
} from './animations/animations';

interface ModalProps {
  title: string;
  subtitle: string;
  name: string;
  forwardActionText: string;

  description?: ReactNode;
  onClose: () => void;
  isOpen: boolean;
  leftContent?: ReactNode;
  leftContentMobile?: boolean;
  moreStyle?: string;
  differentColor?: boolean;
  hideButton?: boolean;
  actionButton?: MouseEventHandler<HTMLButtonElement>;
  actionButton2?: MouseEventHandler<HTMLButtonElement>;
  animateEntry?: boolean;
  secondModalAnimation?: boolean;
  thirdModalAnimation?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  title,
  subtitle,
  name,
  forwardActionText,
  description,
  onClose,
  isOpen,
  leftContent,
  leftContentMobile = false,
  moreStyle = '',
  actionButton,
  // actionButton2,
  differentColor = false,
  // hideButton = false,
  animateEntry = false,
  secondModalAnimation = false,
  thirdModalAnimation = false,
}) => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/10 font-rethink">
      <AnimatePresence>
        <motion.div
          {...(animateEntry && {
            initial: 'hidden',
            animate: 'visible',
            exit: 'exit',
            variants: modalVariants,
          })}
          className="relative   shadow-xl max-w-[800px] max-h-[600px] h-full w-full rounded-[20px] mx-4">
          <button
            type="button"
            onClick={onClose}
            className="absolute top-4 right-4 flex items-center justify-center cursor-pointer">
            <CloseCircle size="33" color="#634C42" variant="Bulk" />
          </button>
          <div className="flex flex-col-reverse overflow-y-auto md:flex-row   bg-white h-full rounded-[20px]">
            <div>
              {leftContent && (
                <div
                  className={`
               ${moreStyle} h-full rounded-l-[20px] md:w-[337px]
              ${leftContentMobile ? ' ' : 'hidden md:block flex-shrink-0'}
            `}>
                  {leftContent}
                </div>
              )}
            </div>
            <div className="p-8 pt-16 pb-12 flex flex-col justify-between md:items-start items-center text-center md:text-start">
              <div>
                <motion.div
                  {...(thirdModalAnimation && {
                    initial: 'hidden',
                    animate: 'visible',
                    variants: textSlideInVariants,
                  })}>
                  <motion.div
                    {...(secondModalAnimation && {
                      variants: textVariants,
                      initial: 'hidden',
                      animate: 'visible',
                    })}>
                    <p
                      className={` text-[32px] font-normal  italic  ${
                        differentColor
                          ? 'text-cus-orange-300'
                          : 'text-primary-400'
                      }`}>
                      {title}
                    </p>
                    <h1
                      className={` text-[40px] -mt-3 capitalize font-bold mb-2 ${
                        differentColor
                          ? 'text-cus-orange-400'
                          : 'text-dark-blue'
                      }`}>
                      {name}
                    </h1>
                  </motion.div>

                  <p className="text-gray-500 uppercase tracking-[0.14em] text-sm mb-5">
                    {subtitle}
                  </p>
                </motion.div>
                <p className="text-grey-950 text-lg mb-8 md:mb-0 border-t  pt-3  border-grey-150">
                  {description}
                </p>
              </div>
              <div className="flex items-center gap-4">
                {/* <Button
                  variant="primary"
                  size="sm"
                  onClick={actionButton2}
                  className={`${
                    hideButton
                      ? 'hidden'
                      : 'bg-cus-pink-500 h-10 md:h-12 px-5 w-fit text-dark-blue-200'
                  }`}
                  iconLeft={
                    <div className="rounded-full   md:p-0.5 bg-dark-blue-200/40">
                      <ArrowLeft size="12" color="#000059" />
                    </div>
                  }>
                  Back
                </Button>{' '} */}
                <Button
                  variant="primary"
                  size="sm"
                  onClick={actionButton}
                  className={`bg-primary-650 h-10 md:h-12 px-5 text-white ${
                    forwardActionText !== 'Continue' && 'px-6.5'
                  }`}
                  iconRight={
                    forwardActionText === 'Continue' ? (
                      <div className="rounded-full md:p-0.5 bg-white/40">
                        <ArrowRight size="12" color="#fff" />
                      </div>
                    ) : undefined
                  }>
                  {forwardActionText}

                </Button>{' '}
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};
