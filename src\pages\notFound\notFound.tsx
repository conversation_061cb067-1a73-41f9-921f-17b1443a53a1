import { ArrowLeft } from 'iconsax-react';
import { useState, useEffect } from 'react';
import { PageTitle } from '../../components/helmet/helmet';

export const NotFound = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen  bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]  flex items-center justify-center px-4 py-12 font-rethink">
      <PageTitle title="Not Found" description="Page not found" />

      <div
        className={`max-w-lg w-full transition-opacity duration-700 ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`}>
        <div className="text-center mb-8">
          <h1 className="text-9xl font-bold text-gray-900 mb-2 tracking-tighter">
            404
          </h1>
          <div className="h-1 w-24 bg-primary mx-auto mb-6"></div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Page Not Found
          </h2>
          <p className="text-gray-600 mb-8">
            The page you are looking for might have been removed, had its name
            changed, or is temporarily unavailable.
          </p>
        </div>

        <button
          onClick={() => window.history.back()}
          className="flex items-center cursor-pointer justify-center rounded-full px-6 py-3 border border-gray-300 bg-primary text-white  transition-colors duration-300 w-full ">
          <ArrowLeft color="#fff" className="h-5 w-5 mr-2" />
          Go Back
        </button>

        <div className="mt-16 flex justify-center items-center">
          <div className="border-t border-gray-300 w-full max-w-xs relative">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-100 px-4">
              <svg
                className="h-6 w-6 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
