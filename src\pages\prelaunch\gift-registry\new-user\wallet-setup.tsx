import { ArrowRight } from 'iconsax-react';
import { useEffect } from 'react';

interface WalletSetupProps {
  onNextStep: () => void;
}

export const WalletSetup = ({ onNextStep }: WalletSetupProps) => {
  useEffect(() => {
    document.body.style.overflow = 'hidden';
        return () => {
      document.body.style.overflow = '';
    };
  }, []);

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="text-2xl md:text-[40px] font-medium">Setup your Wallet</h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <svg
              width="64"
              height="66"
              viewBox="0 0 64 66"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M50.7828 37.1753L54.6027 37.3086L54.6864 37.3184C55.0994 37.3868 55.3773 37.7335 55.3625 38.1589L55.2701 40.8047C55.2541 41.2605 54.9125 41.5941 54.4889 41.6012L50.786 41.472C49.748 41.4357 48.8466 40.6552 48.7027 39.655L48.6844 39.4521L48.6838 39.4423L48.6827 39.1941C48.7098 38.7029 48.901 38.2522 49.1909 37.9044L49.3432 37.7397L49.3533 37.7303C49.7236 37.3508 50.2284 37.1561 50.7828 37.1753Z"
                fill="#CC2200"
                stroke="#B20900"
              />
              <path
                d="M47.1545 35.3565C45.8387 36.6085 45.1613 38.5183 45.5993 40.5466C46.174 43.0299 48.4318 44.6715 50.8712 44.7566L53.0819 44.8338C54.4794 44.8825 55.5813 46.1129 55.5304 47.5679L55.5129 48.0705C55.3215 53.5466 50.871 57.8675 45.6111 57.6839L16.4402 56.6658C11.1803 56.4823 7.04218 51.8616 7.23352 46.3855L7.85558 28.5816C7.96927 25.3277 9.56922 22.4965 11.9444 20.7783C13.5896 19.5644 15.5975 18.8928 17.7574 18.9682L46.9283 19.9863C52.1882 20.1699 56.3263 24.7906 56.1349 30.2667L56.0943 31.4307C56.0434 32.8857 54.8584 34.0362 53.4608 33.9874L50.869 33.897C49.446 33.8473 48.1298 34.3841 47.1545 35.3565Z"
                fill="#CC2200"
              />
              <path
                d="M43.3326 14.3783C43.9937 15.1165 43.3705 16.2072 42.4049 16.1735L22.8908 15.4659C21.7727 15.4269 21.2382 13.978 22.08 13.1863L26.3471 9.01787C29.954 5.54157 35.5951 5.73845 38.9506 9.45775L43.2337 14.2954C43.2582 14.3227 43.3081 14.351 43.3326 14.3783Z"
                fill="#FF9999"
              />
            </svg>
          </div>

          <div className="-mt-12">
            <p className=" mb-6 text-grey-550 text-sm md:text-lg">
              Easily manage your gift contributions and event payments.
            </p>

            <p className="mb-22 text-grey-120 text-sm md:text-lg">
              Set up your wallet to get started—secure, simple, and seamless.
            </p>

            <button
              onClick={onNextStep}
              className="bg-primary-650 text-white py-2.5 px-4 mb-0 rounded-full cursor-pointer flex items-center gap-2 ">
              Create Wallet
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};