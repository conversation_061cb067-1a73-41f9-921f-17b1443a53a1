import ReactOTPInput, { OTPInputProps } from "react-otp-input";
import { twMerge } from "tailwind-merge";

interface Props extends Omit<OTPInputProps, "renderInput"> {
  className?: string;
}

export function OTPInput({ value, onChange, className }: Props) {
  return (
    <ReactOTPInput
      value={value}
      onChange={onChange}
      numInputs={6}
      renderSeparator={<span className="mx-0.5 md:mx-2 inline-block"></span>}
      renderInput={({ className, ...props }) => (
        <input
          className={twMerge(
            `md:h-13 h-11.5 min-w-11.5 md:min-w-13 aspect-square rounded-full border flex items-center justify-center text-3xl md:text-5xl text-dark-100 placeholder:text-grey-200 border-grey-200 font-bold focus:outline-none`,
            className
          )}
          {...props}
          placeholder="0"
        />
      )}
      containerStyle={twMerge(
        `${value === '' ? 'flex max-w-min' : undefined}`,
        className
      )}
      inputType="tel"
    />
  );
}
