export const modalVariants = {
  hidden: {
    y: 300,
    opacity: 0,
    transition: { delay: 0.2 },
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      mass: 1,
      stiffness: 100,
      damping: 20,
      delay: 0.2,
    },
  },
  exit: {
    y: 300,
    opacity: 0,
    transition: { delay: 0.2 },
  },
};
export const textVariants = {
  hidden: { opacity: 0.4 },
  visible: {
    opacity: 1,
    transition: {
      delay: 0.5,
      duration: 0.4,
    },
  },
};
export const textSlideInVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      delay: 0.3,
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};
export const cardSlideInVariants = {
  hidden: {
    x: '20vw',
    opacity: 0,
    rotate: 4,
  },
  visible: {
    x: 0,
    opacity: 1,
    rotate: 0,
    transition: {
      type: 'spring',
      stiffness: 180,
      damping: 20,
      mass: 0.2,
      delay: 0.2,
      velocity: 2,
    },
  },
};

export const fadeInRightTextVariants = {
  hidden: {
    opacity: 0.3,
    x: 2,
    scale: 0.99,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.2, // Reduced duration
      ease: 'easeOut',
      staggerChildren: 0.02, // Reduced stagger time between children
    },
  },
};

export const fadeInRightLetterVariants = {
  hidden: {
    opacity: 0.2,
    x: -4,
    skewX: -2,
  },
  visible: {
    opacity: 1,
    x: 0,
    skewX: 0,
    transition: {
      duration: 0.15, // Reduced duration
      ease: [0.2, 0.65, 0.3, 0.9],
    },
  },
  exit: {
    opacity: 0.2,
    x: 4,
    skewX: 2,
    transition: {
      duration: 0.1, // Reduced exit duration
    },
  },
};
