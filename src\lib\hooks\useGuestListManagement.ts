/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  events,
  GuestData,
  CreateGuestListPayload,
  CreateGuestFilePayload,
  GuestParams,
} from '../services/events';
import { toast } from 'react-toastify';
import { useCallback, useState, useEffect } from 'react';

export const useGuestListManagement = () => {
  const queryClient = useQueryClient();

  const createGuestListMutation = useMutation({
    mutationFn: (payload: CreateGuestListPayload) =>
      events.createGuestList(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created successfully:', response);
      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to create guest list';
      toast.error(errorMessage);
    },
  });

  const createGuestListFromFileMutation = useMutation({
    mutationFn: (payload: CreateGuestFilePayload) =>
      events.createGuestListFromFile(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created from file successfully:', response);
      toast.success('Guest list uploaded successfully!');

      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      console.error('❌ Error uploading guest list:', error);
      const errorMessage =
        error?.response?.data?.message || 'Failed to upload guest list';
      toast.error(errorMessage);
    },
  });

  const createGuestList = useCallback(
    async (
      eventId: string,
      guests: GuestData[],
      inviteType: string = 'manual'
    ) => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      if (!guests || guests.length === 0) {
        throw new Error('At least one guest is required');
      }
      const invalidGuests = guests.filter(
        (guest) =>
          !guest.email ||
          !guest.first_name ||
          !guest.last_name ||
          !guest.phone_number
      );

      if (invalidGuests.length > 0) {
        throw new Error(
          'All guests must have email, first name, last name, and phone number'
        );
      }

      const payload: CreateGuestListPayload = {
        id: eventId,
        guests,
        invite_type: inviteType,
      };

      console.log('🚀 Creating guest list with payload:', payload);
      return createGuestListMutation.mutateAsync(payload);
    },
    [createGuestListMutation]
  );

  const uploadGuestListFile = useCallback(
    async (eventId: string, file: File, inviteType: string = 'file_upload') => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      if (!file) {
        throw new Error('File is required');
      }
      const allowedTypes = ['.csv', '.xlsx', '.xls'];
      const fileExtension = file.name
        .toLowerCase()
        .substring(file.name.lastIndexOf('.'));

      if (!allowedTypes.includes(fileExtension)) {
        throw new Error('Please upload a CSV or Excel file');
      }

      const payload: CreateGuestFilePayload = {
        id: eventId,
        file,
        invite_type: inviteType,
      };

      console.log('📁 Uploading guest list file:', {
        eventId,
        fileName: file.name,
        fileSize: file.size,
      });
      return createGuestListFromFileMutation.mutateAsync(payload);
    },
    [createGuestListFromFileMutation]
  );

  const transformGuestData = useCallback(
    (
      uiGuests: Array<{
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
      }>
    ): GuestData[] => {
      return uiGuests.map((guest) => ({
        email: guest.email.trim().toLowerCase(),
        first_name: guest.firstName.trim(),
        last_name: guest.lastName.trim(),
        phone_number: guest.phone.startsWith('+')
          ? guest.phone
          : `+234${guest.phone}`,
      }));
    },
    []
  );

  return {
    createGuestListMutation,
    createGuestListFromFileMutation,

    createGuestList,
    uploadGuestListFile,
    transformGuestData,

    isCreatingGuestList: createGuestListMutation.isPending,
    isUploadingFile: createGuestListFromFileMutation.isPending,
    isLoading:
      createGuestListMutation.isPending ||
      createGuestListFromFileMutation.isPending,

    createError: createGuestListMutation.error,
    uploadError: createGuestListFromFileMutation.error,
  };
};

export const useGuests = (id: string, params?: GuestParams) => {
  return useQuery({
    queryKey: ['guests', id, params],
    queryFn: () => events.getGuestsForAnAuthUser(id, params),
    enabled: !!id,
  });
};

export const useInfiniteGuests = (id: string, perPage: number = 5) => {
  const [allGuests, setAllGuests] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalGuests, setTotalGuests] = useState(0);

  console.log('🔧 useInfiniteGuests hook state:', {
    id,
    perPage,
    allGuestsCount: allGuests.length,
    currentPage,
    isLoadingMore,
    hasMorePages,
    totalGuests,
  });

  // Initial load
  const {
    data: initialData,
    isLoading: isInitialLoading,
    isError,
  } = useQuery({
    queryKey: ['guests', id, { page: 1, per_page: perPage }],
    queryFn: () =>
      events.getGuestsForAnAuthUser(id, { page: 1, per_page: perPage }),
    enabled: !!id,
  });

  // Set initial data
  useEffect(() => {
    if (initialData?.data) {
      console.log('Initial data received:', {
        guests: initialData.data.guests?.length,
        meta: initialData.data.meta,
        hasNextPage: initialData.data.meta?.next_page,
      });
      setAllGuests(initialData.data.guests || []);
      setTotalGuests(initialData.data.meta?.total || 0);
      setHasMorePages(initialData.data.meta?.next_page || false);
    }
  }, [initialData]);

  // Load more function
  const loadMore = useCallback(
    async (status?: string) => {
      console.log('loadMore called with:', {
        status,
        isLoadingMore,
        hasMorePages,
        id,
        currentPage,
      });

      if (isLoadingMore || !hasMorePages || !id) {
        console.log('loadMore blocked:', { isLoadingMore, hasMorePages, id });
        return;
      }

      setIsLoadingMore(true);
      try {
        const nextPage = currentPage + 1;
        console.log('Fetching page:', nextPage);

        const response = await events.getGuestsForAnAuthUser(id, {
          page: nextPage,
          per_page: perPage,
        });

        console.log('LoadMore response:', {
          guests: response?.data?.guests?.length,
          meta: response?.data?.meta,
          hasNextPage: response?.data?.meta?.next_page,
        });

        if (response?.data?.guests) {
          setAllGuests((prev) => {
            const newGuests = [...prev, ...response.data.guests];
            console.log('Updated guests count:', newGuests.length);
            return newGuests;
          });
          setCurrentPage(nextPage);
          setHasMorePages(response.data.meta?.next_page || false);
        }
      } catch (error) {
        console.error('Error loading more guests:', error);
      } finally {
        setIsLoadingMore(false);
      }
    },
    [id, currentPage, perPage, isLoadingMore, hasMorePages]
  );

  // Reset function for when filters change
  const reset = useCallback(() => {
    setAllGuests([]);
    setCurrentPage(1);
    setIsLoadingMore(false);
    setHasMorePages(true);
  }, []);

  return {
    guests: allGuests,
    totalGuests,
    isLoading: isInitialLoading,
    isLoadingMore,
    hasMorePages,
    isError,
    loadMore,
    reset,
    meta: initialData?.data?.meta,
  };
};
