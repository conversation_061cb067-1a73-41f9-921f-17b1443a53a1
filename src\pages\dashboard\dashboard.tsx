import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '../../components/dashboard/dashboard-layout';

export const Dashboard = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
    
  }, [token, navigate]);
 
  if (!token) {
    return null;
  }

  return (
    <>
      <DashboardLayout />
    </>
  );
};
