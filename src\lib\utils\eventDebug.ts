/* eslint-disable @typescript-eslint/no-explicit-any */
export const eventDebugUtils = {
  checkLocalStorage: () => {
    const eventStore = localStorage.getItem('event-store');
    return eventStore ? JSON.parse(eventStore) : null;
  },

  clearLocalStorage: () => {
    localStorage.removeItem('event-store');
    console.log('🧹 Cleared event-store from localStorage');
  },

  // compareApiWithStorage: (apiData: any) => {
  //   const storageData = eventDebugUtils.checkLocalStorage();
  //   console.log('🔍 Data Comparison:', {
  //     api: {
  //       events: apiData?.data?.events?.length || 0,
  //       meta: apiData?.data?.meta
  //     },
  //     storage: {
  //       events: storageData?.state?.userEvents?.length || 0,
  //       selectedEvent: storageData?.state?.selectedEvent?.title || 'None',
  //       meta: storageData?.state?.eventsMeta
  //     }
  //   });
  // },

  forceRefresh: () => {
    eventDebugUtils.clearLocalStorage();
    if (window.location) {
      window.location.reload();
    }
  },
};

if (typeof window !== 'undefined') {
  (window as any).eventDebug = eventDebugUtils;
}
