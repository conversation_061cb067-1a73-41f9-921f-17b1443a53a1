import { useUserAuthStore } from "../../lib/store/auth";
import logo from '../../assets/icons/Ep-Logo.svg';

export const Head = () => {
      const { userData } = useUserAuthStore();
    
  return (
    <div className="flex justify-between items-center max-w-[550px] mx-auto w-full h-[77px] z-10">
      <img src={logo} alt="logo" />
      <button className="h-12 w-12 bg-white rounded-full flex justify-center items-center cursor-pointer">
        <span className="text-sm text-primary-650 font-bold bg-cus-pink-600 h-8 w-8 flex items-center justify-center rounded-full">
          {userData?.first_name?.charAt(0).toUpperCase() || 'T'}
          {userData?.last_name?.charAt(0).toUpperCase() || 'S'}
        </span>
      </button>
    </div>
  );
}