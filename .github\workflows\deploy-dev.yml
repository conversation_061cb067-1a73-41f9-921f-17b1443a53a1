name: Deploy Dev

on:
  workflow_dispatch:

jobs:
  deploy:
    name: Deploying Dev Server
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Download environment file from S3
        run: |
          aws s3 cp ${{ secrets.AWS_DEV_S3_ENV_FILE }} .env.production

      - name: Build and Push Website
        run: |
          npm run build
          aws s3 sync ./dist s3://${{ secrets.AWS_DEV_S3_BUCKET }} --delete
