import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Facebook, Whatsapp } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';

interface AddGuestsViaLinkProps {
  onFormActiveChange?: (isActive: boolean) => void;
}

export const AddGuestsViaLink = ({
  onFormActiveChange,
}: AddGuestsViaLinkProps) => {
  const [copied, setCopied] = useState(false);
  const eventLink = 'https://EventPark.com/event/invite/tolani-birthday';

  // Track form active state
  useEffect(() => {
    onFormActiveChange?.(copied);
  }, [copied, onFormActiveChange]);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(eventLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="flex-1 pt-8 pb-19 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">
        Add guest via invite link
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-6">
        We've generated a link for you! Share with friends
      </p>

      <div className="bg-gradient-to-b from-[#FEF7F4] via-[#F5F6FE] to-[#EBF3FE] rounded-xl pt-4  mb-6">
        <div className="flex items-center gap-4 mb-4 px-4">
          <div className="w-16 h-16 rounded-lg overflow-hidden">
            <img
              src="/src/assets/images/ex1.png"
              alt="Event"
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h4 className="text-2xl font-medium">Tolani's Birthday</h4>
            <span className="text-grey-950 italic text-[10px] font-semibold">
              📌 12TH OCTOBER 2025
            </span>
          </div>
        </div>

        <div className="bg-black/2 pb-1.5 rounded-2xl">
          <p className=" py-3 px-4 text-grey-250 text-xs">
            You're invited! Join my event and check out the gift registry.
            <br />
            Register now to be part of the celebration!
          </p>

          <div className="flex items-center justify-between mx-1  bg-white rounded-full border border-grey-200 p-2 ">
            <div className="flex items-center">
              <span className="text-primary-650 font-medium mr-1">Link:</span>
              <span className="text-grey-500 text-sm truncate max-w-[200px] md:max-w-[225px]">
                {eventLink}
              </span>
            </div>
            <button
              onClick={handleCopyLink}
              className="flex items-center cursor-pointer gap-0.5 text-primary-650 bg-primary-150 px-2.5 py-1 rounded-full text-sm font-medium">
              {copied ? 'Copied!' : 'Copy'}
              <Copy size={16} color="#4D55F2" variant="Bulk" />
            </button>
          </div>
        </div>
      </div>
      <div>
        <h5 className="text-grey-250 text-xs tracking-[0.12em] mb-3">
          SHARE TO FRIENDS
        </h5>
        <div className="flex gap-3">
          <button className="w-9 h-9 rounded-full cursor-pointer bg-primary-150 flex items-center justify-center">
            <Facebook size={20} color="#4D55F290" variant="Bulk" />
          </button>
          <button className="w-9 h-9 rounded-full cursor-pointer bg-primary-150 flex items-center justify-center">
            <Whatsapp size={20} color="#4D55F290" variant="Bulk" />
          </button>
          <button className="w-9 h-9 rounded-full cursor-pointer bg-primary-150 flex items-center justify-center">
            <Icon name="twitter" />{' '}
          </button>
        </div>
      </div>
    </div>
  );
};
