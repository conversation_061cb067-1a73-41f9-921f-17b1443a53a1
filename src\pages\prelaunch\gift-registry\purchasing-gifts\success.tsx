export const SuccessPayment = () => {
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center relative">
        {/* Confetti Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjIiIGZpbGw9IiNGRkE1MDAiLz4KPGV4dCB4PSIzMCIgeT0iMjAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM0RDU1RjIiLz4KPGNpcmNsZSBjeD0iNTAiIGN5PSIzMCIgcj0iMiIgZmlsbD0iI0ZGNjU2NSIvPgo8cmVjdCB4PSI3MCIgeT0iNDAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM2NEQ5QTQiLz4KPGV4dCB4PSI5MCIgeT0iNTAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiNGRkE1MDAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSI2MCIgcj0iMiIgZmlsbD0iIzRENTVGMiIvPgo8cmVjdCB4PSI0MCIgeT0iNzAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiNGRjY1NjUiLz4KPGV4dCB4PSI2MCIgeT0iODAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM2NEQ5QTQiLz4KPGNpcmNsZSBjeD0iODAiIGN5PSI5MCIgcj0iMiIgZmlsbD0iI0ZGQTUwMCIvPgo8L3N2Zz4=')] opacity-30"></div>
        </div>

        <div className="relative w-full max-w-[450px] mx-auto">
          <div className="relative z-20 bg-white rounded-[20px] text-center shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            {/* Gift Illustration Background */}
            <div className="bg-[#FFE5E5] rounded-t-[20px] h-[200px] w-full flex items-center justify-center relative overflow-hidden">
              {/* Gift Box SVG */}
              <div className="relative">
                <svg
                  width="120"
                  height="120"
                  viewBox="0 0 120 120"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {/* Gift Box Base */}
                  <rect
                    x="20"
                    y="50"
                    width="80"
                    height="60"
                    rx="8"
                    fill="#4D55F2"
                  />
                  <rect
                    x="20"
                    y="50"
                    width="80"
                    height="60"
                    rx="8"
                    fill="url(#giftGradient)"
                  />

                  {/* Gift Box Lid */}
                  <rect
                    x="15"
                    y="40"
                    width="90"
                    height="20"
                    rx="6"
                    fill="#6366F1"
                  />

                  {/* Ribbon Vertical */}
                  <rect x="55" y="20" width="10" height="90" fill="#FF6B6B" />

                  {/* Ribbon Horizontal */}
                  <rect x="10" y="45" width="100" height="10" fill="#FF6B6B" />

                  {/* Bow */}
                  <ellipse cx="50" cy="30" rx="15" ry="8" fill="#FF4444" />
                  <ellipse cx="70" cy="30" rx="15" ry="8" fill="#FF4444" />
                  <circle cx="60" cy="30" r="6" fill="#FF6B6B" />

                  <defs>
                    <linearGradient
                      id="giftGradient"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="100%"
                    >
                      <stop offset="0%" stopColor="#6366F1" />
                      <stop offset="100%" stopColor="#4D55F2" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>

            <div className="flex flex-col items-center text-center py-8 px-6 w-full">
              <h2 className="text-[32px] font-semibold mb-2 text-grey-50">
                You Just gifted
              </h2>
              <p className="text-[18px] text-grey-250 mb-6">
                Olatunde successfully
              </p>

              <p className="text-grey-250 text-base mb-8">
                You just gifted Olatunde an{" "}
                <span className="text-[#4D55F2] font-semibold">
                  Iphone 15 Pro
                </span>
              </p>

              <button
                type="button"
                className="bg-[#4D55F2] cursor-pointer text-base w-full max-w-[306px] text-white py-3 px-6 font-semibold rounded-full hover:bg-[#4D55F2]/90 transition-colors"
              >
                <span>Back to Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
