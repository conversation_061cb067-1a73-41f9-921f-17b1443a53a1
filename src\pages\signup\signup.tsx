import { useRef, useState } from 'react';
import { <PERSON>lide<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>le } from '../../components/slider/slider';
import { AuthLayout } from '../../layouts/auth-layout';
import { StepOne } from './step-one';
import { Progress } from '../../components/progress/progress';
import { StepTwo } from './step-two';
import { StepThree } from './step-three';
import logo from '../../assets/icons/Ep-Logo.svg';
import { PageTitle } from '../../components/helmet/helmet';

type RegistrationData = {
  email: string;
  first_name: string;
  last_name: string;
  expiresAt: string;
};
export function SignupPage() {
  const [currentElement, setCurrentElement] = useState(0);
  const sliderRef = useRef<SliderHandle>(null);
  const [registrationData, setRegistrationData] =
    useState<RegistrationData | null>(null);

  const handleStepOneComplete = (formData: RegistrationData) => {
    setRegistrationData(formData);
    sliderRef.current?.moveLeft();
  };
  return (
    <AuthLayout reverse={false}>
      <PageTitle title="Sign Up" description="Create a new account" />

      <div className="md:p-16 p-8">
        <a
          onClick={(e) => {
            e.preventDefault();
            window.open('https://eventpark.africa/', '_blank');
          }}
          className="flex items-center gap-1.5 mb-[3vh] cursor-pointer">
          <img src={logo} alt="logo" />{' '}
        </a>
        <Progress
          percent={(currentElement + 1) / 3}
          showTracker={false}
          size="small"
          trackClassName="max-w-[90px] mb-5 "
        />
        <Slider
          ref={sliderRef}
          currentElement={currentElement}
          setCurrentElement={setCurrentElement}
          elements={[
            <StepOne handleNext={handleStepOneComplete} />,
            registrationData ? (
              <StepTwo
                handleNext={() => sliderRef.current?.moveLeft()}
                email={registrationData.email}
                first_name={registrationData.first_name}
                last_name={registrationData.last_name}
                expiresAt={registrationData.expiresAt}
              />
            ) : null,
            <StepThree
              email={registrationData?.email || ''}
              first_name={registrationData?.first_name || ''}
              last_name={registrationData?.last_name || ''}
            />,
          ]}
        />
      </div>
    </AuthLayout>
  );
}
