import { EventParkAPI } from '../event-park-api';

export const AuthServices = {
  initiateRegistration: async (data: {
    email: string;
    first_name: string;
    last_name: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/registration/initiate',
      data
    );
  },
  verifyRegistrationOTP: async (data: {
    email: string;
    otp: string | number;
  }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/registration/otp/verify',
      data
    );
  },
  completeRegistration: async (data: {
    email: string;
    first_name: string;
    last_name: string;
    password: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/registration/complete',
      data
    );
  },
  login: async (data: { email: string; password: string }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/access-token/generate',
      data
    );
  },
  passwordResetInitiate: async (data: { email: string }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/password-reset/initiate',
      data
    );
  },
  passwordResetOTP: async (data: { email: string; otp: string | number }) => {
    return await EventParkAPI().post(
      '/v1/user-auth/password-reset/otp/verify',
      data
    );
  },
  completePasswordReset: async (
    data: {
      email: string;
      password: string | number;
    },
    resetToken?: string
  ) => {
    const api = EventParkAPI();
    if (resetToken) {
      api.defaults.headers.Authorization = `Bearer ${resetToken}`;
    }

    return await api.post('/v1/user-auth/password-reset/complete', data);
  },
  initiateGoogleOAuth: async (devicePublicKey: string) => {
    return await EventParkAPI().post('/v1/user-auth/oauth/initiate', {
      provider: 'google',
      device_public_key: devicePublicKey,
      app_type: 'customers',
    });
  },
  completeGoogleOAuth: async (data: {
    challenge: string;
    device_public_key: string;
    signature: string;
  }) => {
    return await EventParkAPI().post('/v1/user-auth/oauth/complete', data);
  },
  getAuthenticatedUser: async () => {
    return await EventParkAPI().get('/v1/user-auth/me');
  },
  getUser: async () => {
    return await EventParkAPI().get('/v1/user');
  },
  updateAuthenticatedUser: async (data: {
    config: {
      completed_onboarding: boolean;
    };
    first_name: string;
    last_name: string;
  }) => {
    return await EventParkAPI().patch('/v1/user', data);
  },
  logout: async () => {
    return await EventParkAPI().post('/v1/user-auth/access-token/blacklist');
  },
  updateUserPassword: async (data: {
    new_password: string;
    old_password: string;
  }) => {
    return await EventParkAPI().post('/v1/user/password/change', data);
  },
  uploadFiles: async (
    file: File,
    module: string = 'profile_picture',
    eventId?: string
  ) => {
    const formData = new FormData();
    formData.append('files[]', file);

    const params: Record<string, string> = { module };
    if (eventId && ['event_image', 'iv_design', 'guestlist'].includes(module)) {
      params.event_id = eventId;
    }

    return await EventParkAPI().post('/v1/user/files', formData, {
      params,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
