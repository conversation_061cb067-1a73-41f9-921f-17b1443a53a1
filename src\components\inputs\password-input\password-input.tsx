import { InputHTMLAttributes, forwardRef } from 'react';
import { Label } from '../label/label';
import { twMerge } from 'tailwind-merge';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label: string;
  error?: string;
}

export const PasswordInput = forwardRef<HTMLInputElement, Props>(
  ({ label, error, className, type = 'password', ...props }, ref) => {
    return (
      <div className='font-rethink'>
        <Label
          id={`label-${props.id}`}
          htmlFor={props.id}
          className="mb-1.5 block">
          {label}
        </Label>
        <input
          className={twMerge(
            'flex p-2.5 pl-3.5 border-grey-200 rounded-full border w-full focus:outline-none disabled:opacity-90 disabled:cursor-not-allowed text-grey-300 placeholder:text-grey-300',
            className
          )}
          type={type}
          ref={ref}
          {...props}
        />
        {error && <p className="text-red-500 mt-1 text-sm">{error}</p>}
      </div>
    );
  }
);


