import { InfoCircle, Tag2, TickCircle } from 'iconsax-react';
import giftIcon from '../../../../assets/images/gift-blur.png';
import { Button } from '../../../../components/button/onboardingButton';
import { useState } from 'react';
import iphone from '../../../../assets/images/iphone.svg';
import cash from '../../../../assets/images/cash-img.png';
import { Footer } from '../../footer';
import { useNavigate } from 'react-router-dom';

export const ViewingGiftAsGuest = () => {
  const [activeTab, setActiveTab] = useState<'items' | 'cash'>('items');
  const navigate = useNavigate();

  const giftItems = [
    {
      id: 1,
      name: 'iPhone 15 Pro',
      price: 1650000,
      image: iphone,
      reserved: false,
      purchased: false,
      mostWanted: true,
      description: 'Flawless makeup for your big day.',
      type: 'standard',
    },
    {
      id: 2,
      name: 'AirPods Pro',
      price: 130000,
      image: iphone,
      reserved: false,
      purchased: true,
      mostWanted: false,
      description: 'Flawless makeup for your big day.',
      type: 'standard',
    },
    {
      id: 3,
      name: 'MacBook Pro',
      price: 2500000,
      image: iphone,
      reserved: true,
      purchased: false,
      mostWanted: false,
      description: 'Powerful laptop for all your needs.',
      type: 'wishlist',
    },
    {
      id: 4,
      name: 'MacBook Pro',
      price: 2500000,
      image: iphone,
      reserved: false,
      purchased: false,
      mostWanted: false,
      description: 'Powerful laptop for all your needs.',
      type: 'wishlist',
      crowdGiftingEnabled: true,
      contributedAmount: 850000,
      contributorCount: 4,
    },
  ];
  const cashItems = [
    {
      id: 1,
      amount: 2000000,
      description: 'Trip to Zanzibar for honeymoon',
      received: 0,
      status: 'available',
    },
    {
      id: 2,
      amount: 1650000,
      description: 'Trip to Zanzibar for honeymoon',
      received: 12,
      status: 'reserved',
    },
    {
      id: 3,
      amount: 1650000,
      description: 'Trip to Zanzibar for honeymoon',
      received: 0,
      status: 'most_wanted',
    },
  ];
  const handleMakePayment = () => {
    navigate('/purchasing-gift-items');
  };
  return (
    <div className="flex pt-20 min-h-screen flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
      <div
        className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
        style={{
          backgroundSize: '100% auto',
        }}
      />

      <div className="mx-auto w-full z-20">
        <div className="  text-center">
          <div className="flex justify-center mb-6">
            <img src={giftIcon} alt="Gift" className="w-16 h-16" />
          </div>

          <div className="text-base uppercase text-gray-500 tracking-[0.16em] mb-5">
            CURATED BY OLADELE JOHNSON
          </div>

          <h1 className="text-5xl font-semibold text-dark-blue-200 mb-3">
            Oladele's birthday gifts
          </h1>

          <p className="text-grey-950 text-lg mb-3">
            Celebrate with me! Pick a gift and <br />
            make my day special. 💙{' '}
          </p>

          <Button
            variant="primary"
            size="sm"
            iconLeft={<InfoCircle size="14" color="#FF6630" variant="Bulk" />}
            className="bg-cus-pink-600 text-cus-orange mx-auto underline italic font-bold">
            How to Get Gift
          </Button>

          <div className="relative flex justify-center mt-14">
            <div className="flex  w-fit">
              <button
                className={`px-5 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeTab === 'items'
                    ? 'bg-[#4D55F2] text-white'
                    : 'text-gray-500'
                }`}
                onClick={() => setActiveTab('items')}>
                Gift Items
              </button>
              <button
                className={`px-5 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeTab === 'cash'
                    ? 'bg-[#4D55F2] text-white'
                    : 'text-gray-500'
                }`}
                onClick={() => setActiveTab('cash')}>
                Cashgifts
              </button>
            </div>

            <div
              className={`absolute bottom-[-14px] w-1.5 h-1.5 bg-[#FF6630] rounded-full transition-all duration-300 ${
                activeTab === 'items'
                  ? 'left-[calc(50%-50px)]'
                  : 'left-[calc(50%+50px)]'
              }`}
            />
          </div>

          <div className="mt-10 mb-20 max-w-[560px] w-full mx-auto">
            {activeTab === 'items' ? (
              <div>
                {' '}
                <div className="space-y-4">
                  {giftItems.map((item) => (
                    <div
                      key={item.id}
                      className="bg-white mx-4 md:mx-0 rounded-xl flex flex-col md:flex-row items-stretch cursor-pointer hover:shadow-lg transition-shadow relative">
                      {item.mostWanted && (
                        <div className="absolute italic text-primary-750 top-2 right-2 bg-primary-150 text-xs font-bold px-2 py-1 rounded-full flex items-center">
                          📍MOST WANTED{' '}
                        </div>
                      )}
                      <div className="md:w-[155px] mt-10 md:mt-0 md:min-h-full bg-gray-200 md:rounded-l-lg overflow-hidden flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          style={{ height: '100%', objectFit: 'cover' }}
                        />
                      </div>
                      <div className="flex-1 text-center md:text-left p-4">
                        <h3 className="text-[22px] font-medium text-grey-750">
                          {item.name}
                        </h3>
                        <p className="text-base text-grey-100">
                          {item.description}
                        </p>

                        <div className="mt-5.5 mb-4 flex flex-col md:flex-row gap-2 items-center">
                          <div className="flex items-center gap-2 bg-light-blue-150 text-orange-700 px-2.5 py-1.5 rounded-full">
                            <Tag2 size={12} variant="Bulk" color="#5925DC" />
                            <span className="text-primary text-sm font-semibold">
                              ₦{item.price.toLocaleString()}
                            </span>
                          </div>

                          {item.reserved && (
                            <div className="flex items-center gap-1 font-bold italic bg-grin text-grin-100 text-sm px-2.5 py-1.5 rounded-full">
                              <svg
                                width="14"
                                height="14"
                                viewBox="0 0 14 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect
                                  x="1"
                                  y="1"
                                  width="12"
                                  height="12"
                                  rx="6"
                                  fill="#3CC35C"
                                />
                                <rect
                                  x="1"
                                  y="1"
                                  width="12"
                                  height="12"
                                  rx="6"
                                  stroke="#3CC35C"
                                  strokeWidth="2"
                                />
                                <path
                                  d="M9.18395 5.36426L6.18395 8.36426L4.82031 7.00062"
                                  stroke="white"
                                  strokeWidth="1.4"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                              <span>Reserved by you</span>
                            </div>
                          )}
                        </div>

                        {/* Crowd Gifting Section */}
                        {item.crowdGiftingEnabled && (
                          <div>
                            <div className="flex items-center gap-1 mb-2">
                              <TickCircle
                                size={16}
                                color="#3CC35C"
                                variant="Bold"
                                className="opacity-50"
                              />
                              <span className="text-sm font-medium text-grey-500">
                                Crowd gifting enabled
                              </span>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full max-w-[226px] bg-gray-200 rounded-full h-2 mb-2">
                              <div
                                className="bg-primary h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${
                                    (item.contributedAmount / item.price) * 100
                                  }%`,
                                }}></div>
                            </div>

                            <p className="text-sm text-grey-950">
                              ₦{item.contributedAmount.toLocaleString()}{' '}
                              contributed by {item.contributorCount} people
                            </p>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex justify-end">
                          {item.purchased ? (
                            <button className="border border-primary-110 italic font-semibold text-sm px-2.5 py-1.5 rounded-full">
                              Complete Purchase
                            </button>
                          ) : (
                            item.reserved &&
                            !item.purchased && (
                              <button
                                onClick={handleMakePayment}
                                className="border border-primary-110 text-white bg-primary italic font-semibold text-sm px-2.5 py-1.5 rounded-full">
                                Make Payment
                              </button>
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                <div className="space-y-4">
                  {cashItems.map((item) => (
                    <div
                      key={item.id}
                      className="bg-white mx-4 md:mx-0 rounded-xl cursor-pointer hover:shadow-lg transition-shadow">
                      <div className="flex flex-col-reverse md:flex-row items-stretch gap-4 min-h-[120px]">
                        {' '}
                        <div className="md:w-[155px] bg-gray-200 md:rounded-l-lg overflow-hidden flex-shrink-0">
                          <img
                            src={cash}
                            className="w-full h-[200px] md:h-full object-cover"
                            style={{ objectFit: 'cover' }}
                          />
                        </div>
                        <div className="flex flex-1 flex-col justify-between  py-4 pr-4">
                          {' '}
                          <div className="text-end ">
                            {item.status === 'available' && (
                              <span className="text-xs font-bold italic text-primary-750 px-2 py-1 rounded-full bg-primary-150 uppercase tracking-wide">
                                Available
                              </span>
                            )}
                            {item.status === 'reserved' && (
                              <span className="text-xs font-bold italic text-green-600 px-2 py-1 rounded-full bg-grin uppercase tracking-wide">
                                Reserved
                              </span>
                            )}
                            {item.status === 'most_wanted' && (
                              <div className="flex justify-end  items-center ">
                                <div className="bg-primary-150 rounded-full px-2 py-1 italic font-bold gap-1">
                                  <span>📍</span>
                                  <span className="text-xs font-medium text-primary-750 uppercase tracking-wide">
                                    Most Wanted
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="flex-1 text-start ">
                            <h3 className="text-xl md:text-[28px] font-extrabold text-grey-750 mb-1">
                              ₦{item.amount.toLocaleString()}
                            </h3>
                            <p className="text-sm max-w-[201px] text-grey-100">
                              {item.description}
                            </p>
                          </div>
                          <div className=" flex w-full justify-end">
                            <button className="border border-primary-110 italic font-semibold text-sm px-2.5 py-1.5 rounded-full">
                              Send Cashgift{' '}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};
