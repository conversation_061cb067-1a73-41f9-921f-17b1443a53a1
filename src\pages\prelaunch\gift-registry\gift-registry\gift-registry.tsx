import { Note } from 'iconsax-react';
import { PageTitle } from '../../../../components/helmet/helmet';
import { useNavigate } from 'react-router-dom';

export const GiftRegistry = () => {
  const navigate = useNavigate();
  const registries = [
    {
      id: 1,
      title: "<PERSON><PERSON><PERSON>'s birthday gifts",
      giftItems: 8,
      cashGifts: 4,
    },
    {
      id: 2,
      title: "<PERSON><PERSON><PERSON>'s birthday gifts",
      giftItems: 8,
      cashGifts: 4,
    },
    {
      id: 3,
      title: "<PERSON><PERSON><PERSON>'s birthday gifts",
      giftItems: 8,
      cashGifts: 4,
    },
    {
      id: 4,
      title: "<PERSON><PERSON><PERSON>'s birthday gifts",
      giftItems: 8,
      cashGifts: 4,
    },
  ];

  const handleViewRegistry = (id: number) => {
    navigate(`/gift-registry/${id}`);
  };

  return (
    <div className="max-w-[800px] mx-auto pt-32 px-4 md:px-0">
      <PageTitle
        title="Gift Registry"
        description="Manage your gift registry"
      />

      <div className="">
        <h1 className="text-2xl font-semibold">Gift Registry</h1>
        <p className="text-grey-950 text-base">
          Welcome back, how can we help today?
        </p>
      </div>

      {registries.length > 0 ? (
        <div className="mb-40 mt-7">
          {registries.map((registry) => (
            <div
              key={registry.id}
              className="bg-white rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] mb-6">
              <div className="flex flex-col md:flex-row  items-center gap-4">
                <div className="grid grid-cols-2 gap-2 p-2 md:border-r border-grey-150">
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                </div>
                <div className="flex-1 text-center md:text-left">
                  <h2 className="md:text-[28px] font-semibold italic">
                    {registry.title}
                  </h2>
                  <p className="text-base text-grey-950 mb-5">
                    <span className="font-bold"> {registry.giftItems}</span>{' '}
                    Gift Items •{' '}
                    <span className="font-bold"> {registry.cashGifts}</span>{' '}
                    Cash Gifts
                  </p>
                  <button
                    onClick={() => handleViewRegistry(registry.id)}
                    className="text-primary border-primary-250 mb-5 md:mb-0 cursor-pointer rounded-full font-semibold text-sm border px-3.5 py-2">
                    View Gift Registry
                  </button>
                </div>
              </div>
            </div>
          ))}

          <div className="flex justify-center">
            <button className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90 mt-6">
              Create Gift Registry
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center mb-40 mt-25">
          <Note size={200} variant="Bulk" color="#B8BBFA" />

          <h2 className="text-xl font-medium mb-3 mt-10">No Items Yet</h2>

          <p className="text-gray-500 text-center mb-10">
            You have no items in your gift registry.
            <br />
            Get started by clicking the button below
          </p>

          <button className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90">
            Create Gift Registry
          </button>
        </div>
      )}
    </div>
  );
};
