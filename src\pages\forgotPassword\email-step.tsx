import { useState } from 'react';
import { StepProps } from './step-props';
import { Button } from '../../components/button/button';

interface EmailStepProps extends StepProps {
  onSubmit: (email: string) => void;
  isLoading: boolean;
}

export const EmailStep = ({ onSubmit, isLoading }: EmailStepProps) => {
  const [email, setEmail] = useState('');
  const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isValidEmail) {
      onSubmit(email);
    }
  };

  return (
    <div className="mb-0 px-[24px]">
      {/* Progress indicator */}
      <div className="flex gap-1 mb-6 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] w-10 bg-primary-750 rounded-full"></div>
        <div className="h-[8px] w-10 bg-gray-200 rounded-full"></div>
        <div className="h-[8px] w-10 bg-gray-200 rounded-full"></div>
        <div className="h-[8px] w-10 bg-gray-200 rounded-full"></div>
      </div>

      {/* Header text */}
      <h3 className="tracking-[0.12em] text-sm text-grey-250 mb-2">
        FORGOT PASSWORD
      </h3>
      <p className="font-semibold text-[32px] mb-4 leading-[96%]">
        It happens to the
        <br /> best of us
      </p>
      <p className="text-grey-250 mb-6 text-base">
        Please enter the email associated with your account to recover your
        forgotten password.
      </p>

      <div>
        <form
          className="flex flex-col grow-[1] h-[324.21px] justify-between"
          onSubmit={handleSubmit}>
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-grey-500 mb-[6px]">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
              className="w-full px-3 py-2 border border-stroke-gray-300 focus-within:outline-none focus-within:border-primary-main rounded-[64px] h-[44px] focus:outline-none focus:ring-transparent"
            />
          </div>
          <Button
            type="submit"
            disabled={!isValidEmail || isLoading}
            variant="primary"
            isLoading={isLoading}>
            Continue
          </Button>
          {/* <button
            type="submit"
            className={`w-full flex cursor-pointer justify-center items-center py-4 rounded-full font-medium ${
              isValidEmail && !isLoading
                ? 'bg-primary-light text-white hover:bg-primary-main'
                : 'bg-primary-light/50 text-white cursor-not-allowed'
            }`}>
            {isLoading ? <ClipLoader size={24} color="#ffffff" /> : 'Continue'}
          </button> */}
        </form>
      </div>
    </div>
  );
};
