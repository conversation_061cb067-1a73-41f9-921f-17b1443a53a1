// import { CloseCircle } from 'iconsax-react';
// import { Icon } from '../icons/icon';

import { Stepper } from "./stepper";

// interface CreateGuestProps {
//   activeStep: number;
//   completedSteps: number[];
//   showSkip?: boolean;
//   onSkip?: () => void;
//   onStepChange: (step: number) => void;
//   onClose?: () => void;
// }

// export const CreateGuest = ({
//   activeStep,
//   completedSteps = [],
//   onStepChange,
//   onClose,
// }: CreateGuestProps) => {
//   const steps = [
//     { id: 1, name: 'Create IV Design' },
//     { id: 2, name: 'Add Guests' },
//     { id: 3, name: 'Preview & Create' },
//   ];

//   const isStepActive = (stepId: number) => activeStep === stepId;
//   const isStepCompleted = (stepId: number) => completedSteps.includes(stepId);
//   const isStepClickable = (stepId: number) =>
//     isStepCompleted(stepId) ||
//     stepId === activeStep ||
//     stepId === activeStep - 1;

//   return (
//     <div className="w-full font-rethink">
//       <div className="flex justify-between items-center px-4 md:px-12 h-[77px] border-b border-gray-200">
//         <h1 className="text-2xl font-medium">Create Guestlist</h1>
//         <div className="text-gray-500">
//           <button
//             onClick={onClose}
//             className="w-10 h-10 cursor-pointer rounded-full bg-white flex justify-center items-center">
//             <CloseCircle color="#FF6630" size="24" variant="Bulk" />
//           </button>
//         </div>
//       </div>

//       <div className="md:fixed xl:left-[calc(20%-40px)] left-3 top-[120px] z-50">
//         <div className=" ">
//           <div className="md:w-[170px] flex px-4 md:px-0 justify-between md:justify-start md:flex-col gap-3">
//             {steps.map((step) => (
//               <div
//                 key={step.id}
//                 className={`flex items-center md:py-2 py-5 ${
//                   isStepClickable(step.id)
//                     ? 'cursor-pointer'
//                     : 'cursor-default opacity-60'
//                 } text-sm ${
//                   isStepActive(step.id) && !isStepCompleted(step.id)
//                     ? 'text-primary-50 font-bold italic'
//                     : isStepCompleted(step.id)
//                     ? 'text-primary-50'
//                     : 'text-grey-250'
//                 }`}
//                 onClick={() => isStepClickable(step.id) && onStepChange(step.id)}>
//                 {isStepActive(step.id) && !isStepCompleted(step.id) && (
//                   <span className="bg-cus-orange-100 h-1.5 w-1.5 rounded-full mr-2"></span>
//                 )}
//                 {isStepCompleted(step.id) && (
//                   <span className="mr-[5px]">
//                     <Icon name="marked" />
//                   </span>
//                 )}
//                 <span>{step.name}</span>
//               </div>
//             ))}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

export const CreateGuest = ({
  activeStep,
  completedSteps = [],
  onStepChange,
  onClose,
}: {
  activeStep: number;
  completedSteps: number[];
  onStepChange: (step: number) => void;
  onClose?: () => void;
}) => {
  const steps = [
    { id: 1, name: 'Create IV Design' },
    { id: 2, name: 'Add Guests' },
    { id: 3, name: 'Preview & Create' },
  ];

  return (
    <Stepper
      steps={steps}
      activeStep={activeStep}
      completedSteps={completedSteps}
      title="Create Guestlist"
      onStepChange={onStepChange}
      onClose={onClose}
    />
  );
};