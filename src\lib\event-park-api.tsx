import axios from 'axios';
import { useUserAuthStore } from './store/auth';
import { isTokenValid } from './helpers';
import { useEventStore } from './store/event';

export const EventParkAPI = () => {
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const token = useUserAuthStore.getState().userAppToken;
  const publicPaths = ['/login', '/signup', '/forgot-password'];

  const axiosInstance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  axiosInstance.interceptors.request.use((config) => {
    const currentPath = window.location.pathname;
    const isPublicPath = publicPaths.includes(currentPath);

    if (token && !isTokenValid(token) && !isPublicPath) {
      useUserAuthStore.getState().clearAuthData();
      useEventStore.getState().clearAllEventData();
      console.error('Session expired, please login again');
      setTimeout(() => (window.location.href = '/login'), 2000);
      return Promise.reject(new Error('Token expired'));
    }
    return config;
  });

  axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
      const currentPath = window.location.pathname;
      const isPublicPath = publicPaths.includes(currentPath);
      if (error.response?.status === 401 && !isPublicPath) {
        useUserAuthStore.getState().clearAuthData();
        useEventStore.getState().clearAllEventData();
        console.error('Session expired, please login again');
        setTimeout(() => (window.location.href = '/login'), 2000);
      }
      return Promise.reject(error);
    }
  );

  return axiosInstance;
};
