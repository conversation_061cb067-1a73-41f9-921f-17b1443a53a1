export const EventGalleryUserCard = () => {
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-b from-[#FEF7F4] to-[#F5F6FE] py-8 px-2">
      {/* Top bar */}
      <div className="w-full max-w-5xl flex flex-col gap-8 items-center">
        {/* Card */}
        <div className="relative bg-white rounded-2xl shadow-2xl flex flex-col md:flex-row w-full max-w-2xl p-8 gap-8">
          {/* Left: Details */}
          <div className="flex-1 flex flex-col gap-4 min-w-[220px]">
            {/* Badges */}
            <div className="flex gap-3 mb-2">
              <div className="flex items-center gap-2 bg-[#FFF4F0] rounded-full px-3 py-1 w-fit">
                <img
                  src="/icons/calendar.svg"
                  alt="calendar"
                  className="w-4 h-4"
                />
                <span className="font-medium text-xs text-transparent bg-clip-text bg-gradient-to-r from-[#FF5519] to-[#FF885E]">
                  October 12, 2025
                </span>
              </div>
              <div className="flex items-center gap-2 bg-[#EDEEFE] rounded-full px-3 py-1 w-fit">
                <img src="/icons/clock.svg" alt="clock" className="w-4 h-4" />
                <span className="font-medium text-xs text-[#4D55F2]">
                  9:30am
                </span>
              </div>
            </div>
            {/* User Info */}
            <div className="flex flex-col gap-3">
              <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
                <span className="text-[#808080] text-base">Fullname</span>
                <span className="font-bold text-base text-black">
                  Adeeko Emmanuel
                </span>
              </div>
              <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
                <span className="text-[#808080] text-base">Email</span>
                <span className="font-bold text-base text-black">
                  <EMAIL>
                </span>
              </div>
              <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
                <span className="text-[#808080] text-base">Mobile Number</span>
                <span className="font-bold text-base text-black">
                  +234 7015263711
                </span>
              </div>
            </div>
            {/* Confirmation */}
            <div className="mt-4 text-center">
              <div className="text-[#000026] text-2xl font-medium mb-1">
                Your Attendance has been confirmed ︎
              </div>
              <div className="text-[#808080] text-base">
                You're all set! Get ready for an amazing event.
                <br />
                Grab your invitation card by clicking the button below
              </div>
            </div>
            {/* Download Button */}
            <div className="flex justify-center mt-4">
              <button className="flex items-center gap-2 bg-[#4D55F2] hover:bg-[#343cd8] text-white font-semibold rounded-full px-8 py-3 text-base shadow-lg transition focus:outline-none focus:ring-2 focus:ring-[#4d55f2] focus:ring-offset-2">
                Download your IV Card
                <img
                  src="/gallery/arrow-iv.svg"
                  alt="arrow down"
                  className="w-5 h-5"
                />
              </button>
            </div>
          </div>
          {/* Right: Images */}
          <div className="flex flex-col gap-4 items-center justify-center min-w-[120px]">
            <img
              src="/gallery/user-card-image-1.png"
              alt="user card 1"
              className="w-32 h-44 object-cover rounded-xl shadow-md"
            />
            <img
              src="/gallery/user-card-image-2.svg"
              alt="user card 2"
              className="w-32 h-10 object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
