import { Calendar, Clock } from 'iconsax-react';
import image from '../../../assets/images/image.png';
import { Icon } from '../../../components/icons/icon';
import { GuestTab } from './guest-tab';
import { useEffect } from 'react';
import { useEventStore } from '../../../lib/store/event';
import { useInfiniteGuests } from '../../../lib/hooks/useGuestListManagement';
import { formatDate, formattingTime } from '../../../lib/helpers';

export const GuestList = () => {
  const { selectedEvent } = useEventStore();
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const {
    guests,
    totalGuests,
    isLoading,
    isLoadingMore,
    hasMorePages,
    isError,
    loadMore,
    meta,
  } = useInfiniteGuests(selectedEvent?.id ?? '', 10);

  console.log('guests data', {
    guests,
    guestsLength: guests?.length,
    totalGuests,
    meta,
    isLoading,
    isLoadingMore,
    hasMorePages,
  });

  const eventDate = formatDate(selectedEvent?.date_from);
  const eventTime = formattingTime(selectedEvent?.date_from);

  // Calculate guest counts by status
  const confirmedCount =
    guests?.filter(
      (guest) => guest?.invite_status?.toLowerCase() === 'confirmed'
    ).length || 0;
  const pendingCount =
    guests?.filter((guest) => guest?.invite_status?.toLowerCase() === 'pending')
      .length || 0;
  const declinedCount =
    guests?.filter(
      (guest) => guest?.invite_status?.toLowerCase() === 'declined'
    ).length || 0;

  const guestStats = [
    {
      label: 'TOTAL GUESTS',
      count: meta?.total || totalGuests || 0,
      icon: <Icon name="profile" />,
    },
    {
      label: 'CONFIRMED',
      count: confirmedCount,
      icon: <Icon name="confirmed" />,
    },
    {
      label: 'PENDING',
      count: pendingCount,
      icon: <Icon name="pending" />,
    },
    {
      label: 'DECLINED',
      count: declinedCount,
      icon: <Icon name="declined" />,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }
  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Sorry, An error occured. Kindly, Refresh</p>{' '}
      </div>
    );
  }
  return (
    <div className="max-w-[560px] w-full mx-auto pt-32 font-rethink px-4 md:px-0">
      <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl">
        <div className="max-w-md">
          <h1 className="text-[28px] font-semibold mb-3">
            {selectedEvent?.title || ''}
          </h1>
          <p className="text-sm text-grey-950 mb-7">
            Bride’s groom for bisola’s wedding ceremony...{' '}
          </p>

          <div className=" flex gap-4 mb-5">
            <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
              <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
              <span className="text-xs italic font-medium text-cus-orange-250">
                {eventDate}
              </span>
            </div>
            <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
              <Clock color="#000073" size={12} variant="Bulk" />{' '}
              <span className="text-dark-blue text-xs italic font-medium">
                {eventTime}
              </span>
            </div>
          </div>
        </div>

        <img
          src={image}
          alt="invite-card"
          className="hidden md:block rounded-br-2xl"
        />
      </div>

      <div className="bg-white rounded-2xl mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-x divide-grey-850">
          {guestStats.map((stat, index) => (
            <div key={index} className=" pr-3 pl-4 pt-3 pb-5">
              <div className="flex justify-end mb-3">{stat.icon}</div>
              <div className="text-[32px] italic font-bold mb-1.5">
                {stat.count}
              </div>
              <div className="text-grey-250 text-xs font-medium uppercase tracking-[0.10em]">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
      <GuestTab
        guests={guests}
        onLoadMore={loadMore}
        isLoadingMore={isLoadingMore}
        hasMorePages={hasMorePages}
      />
    </div>
  );
};

export default GuestList;
