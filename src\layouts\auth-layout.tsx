import { ReactNode } from 'react';
export function AuthLayout({
  children,
  reverse,
  patternFirst = false,
}: {
  children: ReactNode;
  reverse: boolean;
  patternFirst?: boolean;
}) {
  const backgroundDiv = (
    <div className="hidden md:block p-3 h-full">
      <div className="bg-[url('/src/assets/images/bg-auth.png')] bg-cover bg-center rounded-2xl h-full"></div>
    </div>
  );

  const contentDiv = <div className="h-full">{children}</div>;

  return (
    <div
      className={`grid h-screen grid-cols-1 ${
        reverse ? 'md:grid-cols-[1fr_530px]' : 'md:grid-cols-[530px_1fr]'
      }`}>
      {patternFirst ? backgroundDiv : contentDiv}
      {patternFirst ? contentDiv : backgroundDiv}
    </div>
  );
}
