import { ArrowCircleRight2 } from 'iconsax-react';
import { Button } from '../../../../components/button/onboardingButton';
import { useState } from 'react';
import { DeliveryAddress } from './delivery-address';

export const JumiaRedirect = () => {
  const [delivery, setDelivery] = useState(false);
 
  
  return (
    <>
      <div className="px-6 ">
        <h1 className="text-base font-medium mb-3 mt-4">
          You are about to be redirected to{' '}
          <span className="text-cus-orange-800 italic">Jumia.com</span>
        </h1>

        <p className="text-gray-600 mb-10 text-base">Here's what to expect:</p>

        <div className="space-y-4 mb-36">
          <div className="flex items-start gap-4 text-left">
            <div className="flex-shrink-0 w-5.5 h-5.5 bg-primary-650 text-white rounded-full flex items-center justify-center font-semibold text-sm">
              1
            </div>
            <div>
              <p className="text-grey-550 italic text-sm">
                You'll be{' '}
                <span className="font-semibold text-black">
                  redirected to Jumia.com.ng
                </span>{' '}
                <span className="text-gray-500">to purchase your gift</span>
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4 text-left">
            <div className="flex-shrink-0 w-5.5 h-5.5 bg-primary-650 text-white rounded-full flex items-center justify-center font-semibold text-sm">
              2
            </div>
            <div>
              <p className="text-gray-700">
                <span className="text-gray-550 italic  text-sm">
                  After purchase, come back to this tab to
                </span>{' '}
                <span className="font-semibold italic text-sm text-black">
                  confirm your purchase
                </span>
              </p>
            </div>
          </div>
        </div>

        <Button
          variant="primary"
          size="md"
          onClick={() => setDelivery(true)}
          className={`text-white  mb-20 mt-5  bg-primary-650 `}
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }>
          Continue to Jumia
        </Button>
      </div>
      {delivery && <DeliveryAddress onClose={() => setDelivery(false)} />}
    </>
  );
};
