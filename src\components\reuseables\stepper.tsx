import { CloseCircle } from 'iconsax-react';
import { Icon } from '../icons/icon';

interface Step {
  id: number;
  name: string;
}

interface StepperProps {
  steps: Step[];
  activeStep: number;
  completedSteps: number[];
  title?: string;
  onStepChange: (step: number) => void;
  onClose?: () => void;
  showCloseButton?: boolean;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  renderCloseButton?: () => React.ReactNode;
}

export const Stepper = ({
  steps,
  activeStep,
  completedSteps = [],
  title,
  onStepChange,
  onClose,
  showCloseButton = true,
  className = '',
  // orientation = 'vertical',
  renderCloseButton,
}: StepperProps) => {
  const isStepActive = (stepId: number) => activeStep === stepId;
  const isStepCompleted = (stepId: number) => completedSteps.includes(stepId);
  const isStepClickable = (stepId: number) =>
    isStepCompleted(stepId) ||
    stepId === activeStep ||
    stepId === activeStep - 1;

  const defaultCloseButton = () => (
    <button
      onClick={onClose}
      className="w-10 h-10 cursor-pointer rounded-full bg-white flex justify-center items-center">
      <CloseCircle color="#FF6630" size="24" variant="Bulk" />
    </button>
  );

  return (
    <div className={`w-full font-rethink ${className}`}>
      {title && (
        <div className="flex justify-between items-center px-4 md:px-12 h-[77px] border-b border-gray-200">
          <h1 className="text-2xl font-medium">{title}</h1>
          {showCloseButton && (
            <div className="text-gray-500">
              {renderCloseButton ? renderCloseButton() : defaultCloseButton()}
            </div>
          )}
        </div>
      )}

      <div className="md:fixed xl:left-[calc(20%-40px)] left-3 top-[120px] z-50">
        <div className="">
          <div
            className={`md:w-[170px] flex px-4 md:px-0 flex-row flex-wrap mt-8 md:mt-0 md:flex-col justify-center md:justify-start gap-3`}>
            {steps.map((step) => (
              <div
                key={step.id}
                className={`flex items-center md:py-2  ${
                  isStepClickable(step.id)
                    ? 'cursor-pointer'
                    : 'cursor-default opacity-60'
                } text-sm ${
                  isStepActive(step.id) && !isStepCompleted(step.id)
                    ? 'text-primary-50 font-bold italic'
                    : isStepCompleted(step.id)
                    ? 'text-primary-50'
                    : 'text-grey-250'
                }`}
                onClick={() =>
                  isStepClickable(step.id) && onStepChange(step.id)
                }>
                {isStepActive(step.id) && !isStepCompleted(step.id) && (
                  <span className="bg-cus-orange-100 h-1.5 w-1.5 rounded-full mr-2"></span>
                )}
                {isStepCompleted(step.id) && (
                  <span className="mr-[5px]">
                    <Icon name="marked" />
                  </span>
                )}
                <span>{step.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
