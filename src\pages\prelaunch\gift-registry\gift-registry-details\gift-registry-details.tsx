import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useState, useEffect } from "react";
import {
  Tag2,
  ArrowLeft,
  Gift,
  Moneys,
  EyeSlash,
  Box,
  BuyCrypto,
} from "iconsax-react";
import gift from "../../../../assets/images/gift-res.png";
import empty from "../../../../assets/images/empy.png";
import { Icon } from "../../../../components/icons/icon";
import iphone from "../../../../assets/images/iphone.svg";

import { Footer } from "../../footer";
interface GiftItem {
  id: number;
  name: string;
  price: number;
  image: string;
  reserved: number;
  purchased: number;
}

interface CashGift {
  id: number;
  amount: number;
  description: string;
  received: number;
}

interface GiftRegistryData {
  id: number;
  title: string;
  description: string;
  giftItems: number;
  reservedItems: number;
  cashGifts: number;
  receivedCash: number;
  amountReceived: number;
  items: GiftItem[];
  cashItems: CashGift[];
}

export const GiftRegistryDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [registry, setRegistry] = useState<GiftRegistryData | null>(null);
  const [activeTab, setActiveTab] = useState("gifts");
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  const guestStats = [
    {
      label: "GIFT ITEMS",
      count: 468,
      icon: <Icon name="giftItems" />,
    },
    {
      label: "RESERVED ITEMS",
      count: 400,
      icon: <Icon name="giftItems" color="#EBF9EF" secondaryColor="#3CC35C" />,
    },
    {
      label: "CASH GIFTS",
      count: 40,
      icon: <Icon name="cashGift" />,
    },
    {
      label: "RECEIVED CASH",
      count: 28,
      icon: <Icon name="declined" />,
    },
  ];

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setRegistry({
        id: Number(id),
        title: "Oladele's birthday gifts",
        description: "Bride's groom for bisola's wedding ceremony...",
        giftItems: 5,
        reservedItems: 4,
        cashGifts: 8,
        receivedCash: 4,
        amountReceived: 800000,
        items: [
          {
            id: 1,
            name: "iPhone 15 Pro",
            price: 1450000,
            image: iphone,
            reserved: 2,
            purchased: 1,
          },
          {
            id: 2,
            name: "AirPods Pro",
            price: 130000,
            image: "https://via.placeholder.com/150",
            reserved: 2,
            purchased: 0,
          },
        ],
        cashItems: [
          {
            id: 1,
            amount: 500000,
            description: "Round trip to Zanziber and Portugal",
            received: 12,
          },
          {
            id: 2,
            amount: 2000000,
            description: "Round trip to Zanziber and Portugal",
            received: 12,
          },
          {
            id: 3,
            amount: 4500000,
            description: "Round trip to Zanziber and Portugal",
            received: 0,
          },
          {
            id: 4,
            amount: 400000,
            description: "Round trip to Zanziber and Portugal",
            received: 0,
          },
        ],
      });
      setLoading(false);
    });
  }, [id]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen"></div>;
  }

  if (!registry) {
    return (
      <div className="flex justify-center items-center h-screen">
        Registry not found
      </div>
    );
  }

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20 ">
        <PageTitle
          title="Registry Details"
          description="View gift registry details"
        />
        <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer"
          >
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div>
        <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <div className="max-w-md ">
            <h1 className="text-[28px] font-semibold mb-3">
              Oladele’s birthday gifts
            </h1>
            <p className="text-sm text-grey-950 mb-7">
              Bride’s groom for bisola’s wedding <br />
              ceremony...{" "}
            </p>

            <div className=" flex gap-4 mb-5">
              <div className="flex items-center gap-1 bg-primary-250 pl-2.5 pr-2 py-0.5 rounded-2xl">
                <Gift variant="Bulk" size={12} color="#4D55F2" />
                <span className="text-xs italic font-medium text-primary">
                  12 Gift Items
                </span>
              </div>
              <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 rounded-2xl py-0.5">
                <Moneys variant="Bulk" size={12} color="#FF885E" />
                <span className="text-cus-orange-250 text-xs italic font-medium">
                  0 Cash gifts{" "}
                </span>
              </div>
            </div>
          </div>

          <img
            src={gift}
            alt="invite-card"
            className="hidden md:block rounded-br-2xl"
          />
        </div>

        <div className="bg-white rounded-2xl mb-2.5 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-x divide-grey-850">
            {guestStats.map((stat, index) => (
              <div key={index} className=" pr-3 pl-4 pt-3 pb-5">
                <div className="flex justify-end mb-3">{stat.icon}</div>
                <div className="text-[32px] italic font-bold mb-1.5">
                  {stat.count}
                </div>
                <div className="text-grey-250 text-xs font-medium uppercase tracking-[0.10em]">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white flex justify-between py-6 px-4 rounded-xl shadow-[0px_12px_120px_0px_#5F5F5F0F] mb-8">
          <div>
            <p className="text-3xl font-bold italic tracking-[0.04em]">
              {registry.amountReceived.toLocaleString()}
            </p>
            <p className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              Amount Received
            </p>
          </div>
          <button
            className="md:px-4 px-2 py-1 md:py-2 bg-primary rounded-full text-[10px] h-[40px] md:h-auto sm:text-sm font-medium text-white min-w-max w-fit flex items-center gap-2 cursor-pointer "
            onClick={() => navigate("/withdrawal/select-account")}
          >
            Withdraw Balance{" "}
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M9.9974 18.3337C14.5998 18.3337 18.3307 14.6027 18.3307 10.0003C18.3307 5.39795 14.5998 1.66699 9.9974 1.66699C5.39502 1.66699 1.66406 5.39795 1.66406 10.0003C1.66406 14.6027 5.39502 18.3337 9.9974 18.3337Z"
                fill="white"
              />
              <path
                d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                fill="white"
              />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <div className="flex items-center mb-3">
            <span className="text-grey-500 font-medium">Registry Details</span>
            <span className="mx-2 text-grey-500">/</span>
            <span className="font-semibold">
              {activeTab === "gifts" ? "Gift Items" : "Cash Gifts"}
            </span>
          </div>
          <div className="rounded-full p-1 inline-flex bg-gray-100">
            <button
              className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                activeTab === "gifts"
                  ? "bg-primary text-white"
                  : "text-gray-700"
              }`}
              onClick={() => setActiveTab("gifts")}
            >
              Gift Items • {registry.giftItems}
            </button>
            <button
              className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                activeTab === "cash" ? "bg-primary text-white" : "text-gray-700"
              }`}
              onClick={() => setActiveTab("cash")}
            >
              Cash Gifts • {registry.cashGifts}
            </button>
          </div>
        </div>

        {activeTab === "gifts" && (
          <div className="space-y-4">
            {registry.items.map((item) => (
              <div
                key={item.id}
                className="bg-white rounded-xl flex flex-col md:flex-row items-center gap-4.5 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => navigate(`/gift-registry/${id}/gift/${item.id}`)}
              >
                <div className="w-full rounded-t-xl md:rounded-[unset] md:w-[155px] h-[184px] bg-gray-200 md:rounded-l-lg overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1 text-center md:text-left">
                  <h3 className="text-[22px] font-medium text-grey-750">
                    {item.name}
                  </h3>
                  <p className="text-base text-grey-100">
                    Flawless makeup for your big day. 
                  </p>
                  <div className="mt-5.5 mb-4 flex flex-col md:flex-row gap-2 items-center ">
                    <div className="flex items-center gap-2  bg-light-blue-150 text-orange-700 px-2.5 py-1.5 rounded-full">
                      <Tag2 size={12} variant="Bulk" color="#5925DC" />
                      <span className="text-primary text-sm font-semibold">
                        ₦{item.price.toLocaleString()}
                      </span>
                    </div>
                    {item.reserved > 0 && (
                      <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-orange-700 text-sm px-2.5 py-1.5 rounded-full">
                        <Box size={12} variant="Bulk" color="#C4320A" />
                        <span> Reserved • {item.reserved}</span>
                      </div>
                    )}
                    {item.purchased > 0 && (
                      <span className="flex items-center gap-1 font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                        <BuyCrypto size={12} variant="Bulk" color="#3CC35C" />{" "}
                        Purchased • {item.purchased}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-center md:justify-start mb-7 md:mb-0 gap-2">
                    <p className="text-sm text-grey-100">Hide Item from List</p>
                    <EyeSlash variant="Bulk" size={20} color="#999999" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === "cash" &&
          (registry.cashItems && registry.cashItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {registry.cashItems.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-2xl px-4 py-5 cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() =>
                    navigate(`/gift-registry/${id}/cash/${item.id}`)
                  }
                >
                  <Moneys variant="Bulk" size={56} color="#E2CBC1" />
                  <p className="text-[32px] font-bold ">
                    ₦{item.amount.toLocaleString()}
                  </p>
                  <p className="text-grey-250 text-base italic mb-13.5">
                    {item.description}
                  </p>
                  {item.received > 0 && (
                    <div className="flex items-center gap-1 w-fit font-bold italic bg-grin text-grin-100 text-sm px-2.5 py-1.5 rounded-full">
                      <Box size={12} variant="Bulk" color="#3CC35C" />
                      <span> Reserved • {item.received}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white p-14 rounded-2xl flex flex-col items-center justify-center">
              <img src={empty} alt="empty-state" />
              <h3 className="text-[28px] -tracking-[0.04em] font-medium mt-3 mb-2">
                No Cash gifts here currently
              </h3>
              <p className="text-grey-250 text-base mb-5 text-center">
                You have no Cash gifts currently.
                <br /> Get started by clicking the button below
              </p>
              <button className="bg-primary text-white font-medium py-2 px-4 rounded-full mb-25">
                Add Cash gift
              </button>
            </div>
          ))}
      </div>
      <Footer />
    </div>
  );
};
