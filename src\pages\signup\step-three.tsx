/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation } from '@tanstack/react-query';
import { Button } from '../../components/button/button';
import { PasswordInput } from '../../components/inputs/password-input/password-input';
import { AuthServices } from '../../lib/services/auth';
import { toast } from 'react-toastify';
import { useForm } from 'react-hook-form';
// import { useUserAuthStore } from '../../lib/store/auth';
import { useNavigate } from 'react-router-dom';

type FormData = {
  password: string;
  confirmPassword: string;
};
export function StepThree({
  email,
  first_name,
  last_name,
}: {
  email: string;
  first_name: string;
  last_name: string;
}) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<FormData>({ mode: 'onChange' });
  // const {  userData, setAuthData } = useUserAuthStore();
  const navigate = useNavigate();
  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.completeRegistration({
        email: email,
        first_name: first_name,
        last_name: last_name,
        password: data.password,
      }),
    onSuccess: () => {
      navigate('/login');
      toast.success('Account created successfully, Proceed to Login');
      // const access_token = data?.data?.access_token;
      // setAuthData(access_token, {
      //   ...userData!,
      //   password_set: data?.data?.password_set,
      //   email,
      //   first_name,
      //   last_name,
      //   id: data?.data?.id,
      // });
      // navigate('/onboarding', {
      //   state: {
      //     first_name: first_name,
      //   },
      // });
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const onSubmit = handleSubmit((data) => {
    mutation.mutate(data);
  });
  return (
    <div>
      <div className="mb-[4vh]">
        <h1 className="font-semibold text-[32px]">Setup a Password</h1>
        <h3 className="text-grey-100 mb-9">
          Bringing your event from planning to completion!
        </h3>

        <form onSubmit={onSubmit}>
          <PasswordInput
            label="New Password"
            id="signup-password"
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 8,
                message: 'Password must be at least 8 characters',
              },
              // pattern: {
              //   value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/,
              //   message:
              //     'Password must contain uppercase, lowercase, and number',
              // },
            })}
            placeholder="Enter your password"
            error={errors.password?.message}
          />
          <div className="mt-5">
            <PasswordInput
              label="Confirm Password"
              id="signup-confirm-password"
              {...register('confirmPassword', {
                required: 'Please confirm your password',
                validate: (value) =>
                  value === watch('password') || 'Passwords do not match',
              })}
              placeholder="Re-enter your Password"
              error={errors.confirmPassword?.message}
            />
          </div>
          <Button
            type="submit"
            variant="primary"
            className="mt-10"
            disabled={!isValid || mutation.isPending}
            isLoading={mutation.isPending}>
            Create Account
          </Button>
        </form>
      </div>
    </div>
  );
}
