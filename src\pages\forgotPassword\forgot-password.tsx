/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import { toast } from 'react-toastify';
import { EmailStep } from './email-step';
import { OTPStep } from './otp-step';
import { NewPasswordStep } from './new-password';
import logo from '../../assets/icons/Ep-Logo.svg';
import successGIF from '../../assets/animations/gift.gif';
import { useNavigate } from 'react-router-dom';
import { AuthServices } from '../../lib/services/auth';
import { useMutation } from '@tanstack/react-query';
import { SuccessStep } from './success-step';
import bg from '../../assets/images/bg-success.png';

type EmailReset = {
  email: string;
};
export const ForgotPassword = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [email, setEmail] = useState('');
  const navigate = useNavigate();

  const initiateReset = useMutation({
    mutationFn: (data: EmailReset) =>
      AuthServices.passwordResetInitiate({
        email: data.email,
      }),
    onSuccess: (data) => {
      localStorage.setItem('reset_expiry', data?.data?.expires_at);
      setCurrentStep(2);
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleEmailSubmit = (email: string) => {
    initiateReset.mutate({ email });
    setEmail(email);
  };

  return (
    <div className="flex flex-col pb-52 font-rethink items-center justify-center relative shadow-[0px_12px_120px_0px_#5F5F5F0F] bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]  w-full ">
      <div className="absolute inset-0 "></div>
      {currentStep === 4 && (
        <img
          src={successGIF}
          alt="gif"
          className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
        />
      )}
      {currentStep === 4 && (
        <img
          src={bg}
          alt="success"
          className="w-full transition-all h-fit  opacity-40 absolute object-contain top-32 left-0 right-0"
        />
      )}

      <div
        onClick={() => {
          navigate('/');
        }}
        className="flex mt-10 items-center gap-3 mb-14  cursor-pointer">
        <img src={logo} alt="EventPark logo" className="h-8 " />
      </div>
      <div
        className={`bg-white w-full z-20 max-w-[450px] rounded-[28px] ${
          currentStep === 4 ? 'py-0' : 'py-[32px]'
        }`}>
        {currentStep === 1 && (
          <EmailStep
            onSubmit={handleEmailSubmit}
            isLoading={initiateReset.isPending}
          />
        )}
        {currentStep === 2 && (
          <OTPStep email={email} thirdStep={() => setCurrentStep(3)} />
        )}
        {currentStep === 3 && (
          <NewPasswordStep email={email} fourthStep={() => setCurrentStep(4)} />
        )}
        {currentStep === 4 && <SuccessStep />}
      </div>
    </div>
  );
};
