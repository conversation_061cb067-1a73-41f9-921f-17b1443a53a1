import { InputHTMLAttributes, forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';

interface FormInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  text?: string;
  leftAddon?: React.ReactNode;
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  ({ label, error, text, className, leftAddon, ...props }, ref) => {
    return (
      <div className="mb-4 font-rethink">
        {label && (
          <label className="block mb-1.5 text-sm font-medium text-grey-500">
            {label}
          </label>
        )}
        <div className={`${leftAddon ? 'flex' : ''}`}>
          {leftAddon && (
            <div className="flex items-center  h-[44px] pl-3.5 pr-2.5 rounded-l-full border border-r-0 border-gray-200 bg-white">
              {leftAddon}
            </div>
          )}
          <input
            ref={ref}
            className={twMerge(
              `w-full h-[44px] pl-3.5 pr-2.5 ${
                leftAddon ? 'rounded-r-full' : 'rounded-full'
              } text-base outline-none border border-grey-200 placeholder:text-grey-300`,
              className
            )}
            {...props}
          />
        </div>
        {error && <p className="text-red-500 mt-1 text-sm">{error}</p>}
        {text && <p className="text-xs italic text-grey-650 mt-1.5">{text}</p>}
      </div>
    );
  }
);
