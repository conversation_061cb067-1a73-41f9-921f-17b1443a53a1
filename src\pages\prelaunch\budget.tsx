import { motion } from 'framer-motion';
import { useState } from 'react';
import round from '../../assets/images/round.png';

export default function BudgetPriceBubbles() {
  const [budgetItems] = useState([
        {
          id: 1,
          amount: '₦1,650,000',
          category: '🍷 Catering',
          rotation: '-11.99deg',
          top: '73px',
          left: '131px',
        },
        {
          id: 2,
          amount: '₦1,650,000',
          category: '🔮 Entertainment',
          rotation: '0deg',
          top: '251px',
          left: '22px',
        },
        {
          id: 3,
          amount: '₦1,650,000',
          category: '🏬 Venue',
          rotation: '15.26deg',
          top: '470px',
          left: '145px',
        },
  ]);

  // Animation variants (added only this new part)
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const imageVariants = {
    hidden: { x: 100, opacity: 0 },
    show: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
      },
    },
  };

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    show: (i: number) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: i * 0.15,
        type: 'spring',
        stiffness: 100,
        damping: 15,
      },
    }),
  };

  return (
    <div className="relative h-full w-full overflow-hidden bg-[linear-gradient(179.62deg,#FFFCFB_50.04%,#FEF7F4_120.58%)]">
      {/* Animated Image - Only added motion wrapper */}
      <motion.div
        initial="hidden"
        animate="show"
        variants={imageVariants}
        className="absolute w-full h-full left-25 top-15" // Your exact classes
      >
        <img src={round} alt="budget" />
      </motion.div>

      {/* Animated Cards - Only added motion wrappers */}
      <motion.div
        initial="hidden"
        animate="show"
        variants={containerVariants}
        className="contents" 
      >
        {budgetItems.map((item, index) => (
          <motion.div
            key={item.id}
            custom={index}
            variants={itemVariants}
            className="absolute shadow-lg backdrop-blur-md rounded-lg" 
            animate={{
              rotate: item.rotation,
              x: 0, 
              opacity: 1, 
            }}
            style={{
              width:
                item.id === 1 ? '128px' : item.id === 2 ? '124px' : '132px',
              height: item.id === 3 ? '70px' : '65px',
              left: item.left,
              top: item.top,
            }}>
            <div
              className={`flex flex-col items-center p-3 bg-white rounded-lg ${
                item.id === 3 ? 'h-16' : 'h-full'
              } w-full`}>
              <div className="flex flex-col items-center gap-1">
                <div className="font-extrabold text-base tracking-tight text-[#414651]">
                  {item.amount}
                </div>
                <div className="italic text-xs text-[#535862]">
                  {item.category}
                </div>
              </div>
            </div>

            {/* Your exact pointer triangles */}
            {item.id === 1 && (
              <div className="mx-auto">
                <div className="absolute w-3 h-3 bg-white rounded-sm transform rotate-45 left-1/2 -translate-x-1/2 -bottom-1"></div>
              </div>
            )}
            {item.id === 2 && (
              <div
                className="absolute w-3 h-3 bg-white rounded-sm transform"
                style={{
                  right: '-5px',
                  top: '50%',
                  transform: 'translateY(-50%) rotate(-45deg)',
                }}></div>
            )}
            {item.id === 3 && (
              <div className="w-3 h-1.5 mx-auto">
                <div
                  className="absolute w-3 h-3 bg-white rounded-sm"
                  style={{
                    bottom: '-24.5px',
                    left: 'calc(50% - 6px + 2.8px)',
                    transform: 'matrix(-0.5, -0.87, 0.87, -0.5, 0, 0)',
                  }}></div>
              </div>
            )}
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}