import { useState } from 'react';
import { Button } from '../../components/button/onboardingButton';
import { ArrowCircleRight2 } from 'iconsax-react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  fadeInRightTextVariants,
  fadeInRightLetterVariants
} from '../../components/reuseables/animations/animations';
import gift from '../../assets/images/giftbox.png';
import guest from '../../assets/images/guest-man.png';
import planner from '../../assets/images/planner.png';

interface EventToolsSelectionProps {
  onNext: (tools: string[]) => void;
  initialData?: string[];
  direction: 'forward' | 'backward';
}

export const EventToolsSelection = ({ onNext, initialData, direction }: EventToolsSelectionProps) => {
  const [selectedTools, setSelectedTools] = useState<string[]>(initialData || []);

  const handleToolSelection = (tool: string) => {
    setSelectedTools(prev => 
      prev.includes(tool)
        ? prev.filter(t => t !== tool)
        : [...prev, tool]
    );
  };

  const handleContinue = () => {
    if (selectedTools.length > 0) {
      onNext(selectedTools);
    }
  };

  const isToolSelected = (tool: string) => selectedTools.includes(tool);

  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      x: direction === 'forward' ? '20vw' : '-20vw',
      rotate: direction === 'forward' ? 4 : -4,
      opacity: 0,
    },
    visible: {
      x: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 20,
        mass: 0.2,
        delay: 0.2,
        velocity: 2,
      },
    },
  });

  return (
    <div>
      <motion.h2 
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        variants={fadeInRightTextVariants}
      >
        <AnimatePresence mode="wait">
          {["Pick", "the", "tools", "you", "need."].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{ 
                display: 'inline-block', 
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative'
              }}
            >
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}
              >
                {word}
              </motion.span>
            </motion.span>
          ))}
          <br />
          {["Add", "more", "later!"].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{ 
                display: 'inline-block', 
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative'
              }}
            >
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}
              >
                {word}
              </motion.span>
            </motion.span>
          ))}
        </AnimatePresence>
      </motion.h2>
      <motion.div 
        className="bg-white pt-3 px-2.5 pb-6 rounded-[20px]"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}
      >
        <div className="flex justify-center rounded-xl border border-grey-150">
          <div
            className={`h-[227px] ${
              isToolSelected('registry') ? 'bg-primary-150' : ''
            } flex flex-col justify-between max-w-[176px] w-full relative rounded-l-xl`}>
            <div>
              <div className="flex justify-end mt-3">
                <div
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-3 cursor-pointer
                    ${
                      isToolSelected('registry')
                        ? 'bg-primary-650'
                        : 'bg-white border border-primary-110'
                    }`}
                  onClick={() => handleToolSelection('registry')}>
                  {isToolSelected('registry') && (
                    <svg
                      width={24}
                      height={24}
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M9 12L11 14L15 10"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>
              <h2
                className={`text-sm md:text-xl text-grey-550 ${
                  isToolSelected('registry') ? 'text-primary-120' : ''
                } font-medium pl-3 leading-[100%]`}>
                Gift
                <br />{' '}
                <span
                  className={`text-base md:text-2xl text-black font-semibold ${
                    isToolSelected('registry') ? 'text-dark-blue' : ''
                  } `}>
                  Registry
                </span>
              </h2>
            </div>
            <img src={gift} alt="gift" className="absolute bottom-0" />
          </div>
          <div
            className={`h-[227px] ${
              isToolSelected('manager') ? 'bg-cus-pink-50' : ''
            } border-r border-l border-grey-150 max-w-[176px] w-full flex flex-col justify-between relative`}>
            <div>
              <div className="flex justify-end mt-3">
                <div
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-3 cursor-pointer
                    ${
                      isToolSelected('manager')
                        ? 'bg-cus-orange'
                        : 'bg-white border border-primary-110'
                    }`}
                  onClick={() => handleToolSelection('manager')}>
                  {isToolSelected('manager') && (
                    <svg
                      width={24}
                      height={24}
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M9 12L11 14L15 10"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>
              <h2
                className={`text-sm md:text-xl ${
                  isToolSelected('manager')
                    ? 'text-cus-orange-300'
                    : 'text-grey-550'
                } font-medium pl-3 leading-[100%]`}>
                Guest
                <br />{' '}
                <span
                  className={`text-base md:text-2xl text-black font-semibold ${
                    isToolSelected('manager') ? 'text-cus-red' : ''
                  }`}>
                  Manager
                </span>
              </h2>
            </div>
            <img src={guest} alt="guest" className="absolute bottom-0" />
          </div>
          <div
            className={`flex flex-col justify-between max-w-[176px] w-full h-[227px] relative rounded-r-xl ${
              isToolSelected('planner') ? 'bg-primary-130' : ''
            }`}>
            <div>
              <div className="flex justify-end mt-3">
                <div
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-3 cursor-pointer
                    ${
                      isToolSelected('planner')
                        ? 'bg-primary-140'
                        : 'bg-white border border-primary-110'
                    }`}
                  onClick={() => handleToolSelection('planner')}>
                  {isToolSelected('planner') && (
                    <svg
                      width={20}
                      height={20}
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M9 12L11 14L15 10"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>
              <h2
                className={`text-sm md:text-xl ${
                  isToolSelected('planner') ? 'text-primary-120' : 'text-grey-550'
                } font-medium pl-3 leading-[100%]`}>
                Budget
                <br />{' '}
                <span
                  className={`text-base md:text-2xl text-black font-semibold ${
                    isToolSelected('planner') ? 'text-dark-blue' : ''
                  }`}>
                  Planner
                </span>
              </h2>
            </div>
            <img src={planner} alt="" className="absolute bottom-0 right-0" />
          </div>
        </div>
        <Button
          variant="primary"
          size="md"
          className={`text-white mt-13 ${
            selectedTools.length > 0 ? 'bg-primary-650' : '!bg-primary-650/35'
          }`}
          onClick={handleContinue}
          disabled={selectedTools.length === 0}
          iconRight={<ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />}>
          Continue
        </Button>
      </motion.div>
    </div>
  );
};
