import { useParams, useNavigate } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useState, useEffect } from "react";
import { Tag2, <PERSON>Left, Box } from "iconsax-react";
import iphone from "../../../../assets/images/iphone.svg";
import { Footer } from "../../footer";

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: "Reserved" | "Received";
}

interface GiftItem {
  id: number;
  name: string;
  price: number;
  image: string;
  reserved: number;
  purchased: number;
  description: string;
  contributors: Contributor[];
  amountReceived: number;
}

export const GiftItemDetails = () => {
  const { registryId, giftId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [giftItem, setGiftItem] = useState<GiftItem | null>(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      // Mock data - in real app, fetch based on registryId and giftId
      setGiftItem({
        id: Number(giftId),
        name: "iPhone 15 Pro",
        price: 1450000,
        image: iphone,
        reserved: 2,
        purchased: 1,
        description: "Flawless makeup for your big day.",
        amountReceived: 800000,
        contributors: [
          {
            id: 1,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Reserved" as const,
          },
          {
            id: 2,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Reserved" as const,
          },
          {
            id: 3,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Reserved" as const,
          },
        ],
      });
      setLoading(false);
    }, 500);
  }, [registryId, giftId]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen"></div>;
  }

  if (!giftItem) {
    return (
      <div className="flex justify-center items-center h-screen">
        Gift item not found
      </div>
    );
  }

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20">
        <PageTitle
          title="Gift Item Details"
          description="View gift item details"
        />
        <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer"
          >
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
          {/* Product Header */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="w-[155px] h-[184px] bg-[#C4B5E0] rounded-2xl overflow-hidden flex-shrink-0">
              <img
                src={giftItem.image}
                alt={giftItem.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <h3 className="text-[22px] font-medium text-grey-750 mb-2">
                {giftItem.name}
              </h3>
              <p className="text-base text-[#535862] mb-4">
                {giftItem.description}
              </p>
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center gap-2 bg-light-blue-150 text-[#B54708] px-2.5 py-1.5 rounded-full">
                  <Tag2 size={12} variant="Bulk" color="#5925DC" />
                  <span className="text-primary text-sm font-semibold">
                    ₦{giftItem.price.toLocaleString()}
                  </span>
                </div>
                {giftItem.reserved > 0 && (
                  <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-[#B54708] text-sm px-2.5 py-1.5 rounded-full">
                    <Box size={12} variant="Bulk" color="#C4320A" />
                    <span> Reserved • {giftItem.reserved}</span>
                  </div>
                )}
              </div>
              <div className="text-sm text-green-600 mb-2">
                ✓ Crowd gifting enabled
              </div>
            </div>
          </div>

          {/* Amount Received */}
          <div className="mb-6">
            <div className="text-[32px] font-bold text-black mb-1">
              {giftItem.amountReceived.toLocaleString()}
            </div>
            <div className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              AMOUNT RECEIVED
            </div>
          </div>

          {/* Contributors Section */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contributors</h4>
            <div className="space-y-3">
              {giftItem.contributors.map((contributor) => (
                <div
                  key={contributor.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#F5F6FE] rounded-full flex items-center justify-center">
                      <span className="text-primary text-base  font-semibold">
                        OR
                      </span>
                    </div>
                    <div>
                      <div className=" text-[#00008C] font-semibold text-sm">
                        {contributor.name}
                      </div>
                      <div className="text-[12px] text-[#535862]">
                        {contributor.email}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-[#B54708] italic">
                    {contributor.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};
