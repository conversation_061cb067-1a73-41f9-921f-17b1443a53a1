import { ArrowDown2 } from 'iconsax-react';
import { useState } from 'react';

export default function AllBudgets() {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasData = true;
  const TOTAL_STROKES = 60;
  const percentSpent = 40;
  return (
    <div className="w-full max-w-xl mb-20 mx-auto bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 mt-6">
      <div
        className="flex items-center gap-1 mb-4 cursor-pointer "
        onClick={() => setIsExpanded(!isExpanded)}>
        <h2
          className={`font-extrabold  text-xs italic  ${
            hasData ? 'text-cus-orange-100' : 'text-primary'
          }`}>
          ALL BUDGETS
        </h2>
        <ArrowDown2 color="#000" size={16} />
      </div>

      {isExpanded && (
        <div className="">
          <div className="flex justify-between  pb-6 border-b border-grey-150">
            <div className="max-w-[247px] w-full border-r border-primary-250">
              <p className="text-grey-250 text-xs md:tracking-[0.10em] font-medium mb-1">
                AMOUNT BUDGETED
              </p>
              <p className="text-sm md:text-xl font-bold">
                ₦{hasData ? '10,000,000' : '0.00'}
                <span className="text-primary-400 text-lg">.00</span>
              </p>
            </div>
            <div className="max-w-[247px] w-full ">
              <p className="text-grey-250 text-xs md:tracking-[0.10em] font-medium mb-1">
                AMOUNT SPENT
              </p>
              <p className="text-sm md:text-xl font-bold">
                ₦{hasData ? '2,500,000' : '0.00'}
                <span className=" text-lg">.00</span>
              </p>
            </div>
          </div>

          <div className="h-14 w-full flex gap-1 px-0.5 mt-4">
            {Array.from({ length: TOTAL_STROKES }).map((_, index) => {
              const isSpent =
                index < Math.round((percentSpent / 100) * TOTAL_STROKES);
              return (
                <div
                  key={`stroke-${index}`}
                  className={`h-full flex-1 rounded-full ${
                    isSpent ? 'bg-primary-300' : ' bg-cus-pink-300'
                  }`}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
