import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  StepProgress,
  Step,
} from "../../../../components/step-progress/step-progress";
import { Footer } from "../../footer";

// Bank illustration SVG components
const BankIllustrationDefault: React.FC = () => (
  <svg
    width="98"
    height="86"
    viewBox="0 0 98 86"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path opacity="0.6" d="M40 55H20V90H40V55Z" fill="#EDEEFE" />
    <path opacity="0.4" d="M60 55H40V90H60V55Z" fill="#EDEEFE" />
    <path opacity="0.6" d="M80 55H60V90H80V55Z" fill="#EDEEFE" />
    <path opacity="0.4" d="M100 55H80V90H100V55Z" fill="#EDEEFE" />
    <path
      d="M106.85 28.7491L61.85 10.7492C60.85 10.3492 59.15 10.3492 58.15 10.7492L13.15 28.7491C11.4 29.4491 10 31.4991 10 33.3991V49.9991C10 52.7491 12.25 54.9991 15 54.9991H105C107.75 54.9991 110 52.7491 110 49.9991V33.3991C110 31.4991 108.6 29.4491 106.85 28.7491ZM60 42.4991C55.85 42.4991 52.5 39.1491 52.5 34.9991C52.5 30.8491 55.85 27.4991 60 27.4991C64.15 27.4991 67.5 30.8491 67.5 34.9991C67.5 39.1491 64.15 42.4991 60 42.4991Z"
      fill="#EDEEFE"
    />
  </svg>
);

const BankIllustrationSelected: React.FC = () => (
  <svg
    width="98"
    height="86"
    viewBox="0 0 98 86"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path opacity="0.6" d="M40 55H20V90H40V55Z" fill="#A6AAF9" />
    <path opacity="0.4" d="M60 55H40V90H60V55Z" fill="#A6AAF9" />
    <path opacity="0.6" d="M80 55H60V90H80V55Z" fill="#A6AAF9" />
    <path opacity="0.4" d="M100 55H80V90H100V55Z" fill="#A6AAF9" />
    <path
      d="M106.85 28.7491L61.85 10.7492C60.85 10.3492 59.15 10.3492 58.15 10.7492L13.15 28.7491C11.4 29.4491 10 31.4991 10 33.3991V49.9991C10 52.7491 12.25 54.9991 15 54.9991H105C107.75 54.9991 110 52.7491 110 49.9991V33.3991C110 31.4991 108.6 29.4491 106.85 28.7491ZM60 42.4991C55.85 42.4991 52.5 39.1491 52.5 34.9991C52.5 30.8491 55.85 27.4991 60 27.4991C64.15 27.4991 67.5 30.8491 67.5 34.9991C67.5 39.1491 64.15 42.4991 60 42.4991Z"
      fill="#A6AAF9"
    />
  </svg>
);

export const SelectAccount: React.FC = () => {
  const [selectedAccount, setSelectedAccount] = useState("");
  const [showAddAccount, setShowAddAccount] = useState(false);
  const [bankName, setBankName] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [accountName, setAccountName] = useState("");
  const [isFirstTimeUser, setIsFirstTimeUser] = useState(true); // For demo, set to true
  const [showCreatePin, setShowCreatePin] = useState(false);
  const [showPinSuccess, setShowPinSuccess] = useState(false);
  const [newPin, setNewPin] = useState(["", "", "", ""]);
  const navigate = useNavigate();

  // Check if first time user on component mount
  useEffect(() => {
    if (isFirstTimeUser) {
      setShowCreatePin(true);
    }
  }, [isFirstTimeUser]);

  const steps: Step[] = showCreatePin
    ? [
        { id: 1, name: "Create Transaction Pin" },
        { id: 2, name: "Select Account" },
        { id: 3, name: "Authenticate" },
      ]
    : [
        { id: 1, name: "Select Account" },
        { id: 2, name: "Authenticate" },
      ];

  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = "fixed";
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
      window.scrollTo(0, parseInt(scrollY || "0") * -1);
    };
  }, []);

  const handleCancel = () => {
    navigate(-1);
  };

  const handleContinue = () => {
    if (
      selectedAccount &&
      (selectedAccount === "access" || selectedAccount === "gt")
    ) {
      navigate("/withdrawal/amount");
    }
  };

  const handleAddAccount = () => {
    setShowAddAccount(true);
  };

  const handleBackToSelect = () => {
    setShowAddAccount(false);
    setBankName("");
    setAccountNumber("");
    setAccountName("");
  };

  const handleAddAccountSubmit = () => {
    // Handle adding the new account
    console.log("Adding account:", { bankName, accountNumber, accountName });
    // For now, just go back to select
    handleBackToSelect();
  };

  const handlePinInputChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newPinArray = [...newPin];
      newPinArray[index] = value;
      setNewPin(newPinArray);

      // Auto-focus next input
      if (value && index < 3) {
        const nextInput = document.getElementById(`pin-input-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleSetPin = () => {
    const pinString = newPin.join("");
    if (pinString.length === 4) {
      // Simulate setting PIN
      console.log("Setting PIN:", pinString);
      setShowCreatePin(false);
      setShowPinSuccess(true);
    }
  };

  const handlePinSuccessClose = () => {
    setShowPinSuccess(false);
    setIsFirstTimeUser(false);
    // Reset to show select account
  };

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto bg-white">
      <div className="flex flex-col w-full font-rethink min-h-screen">
        <div
          className={`fixed top-0 left-0 px-4 md:px-0 right-0 z-50 max-w-[560px] w-full mx-auto`}
        >
          <div className="h-[48px] absolute top-[-50px] bg-transparent w-full max-w-[443px] blur-xl [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] group-scroll:shadow-none group-scroll:bg-white/10 group-scroll:backdrop-blur-md"></div>
          <div className="">
            <div className="flex justify-end pt-13 pb-5 ">
              <button
                onClick={handleCancel}
                className="px-6 py-3 cursor-pointer rounded-full bg-primary-250 text-primary font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

        <div className="max-w-[560px] w-full mx-auto pt-32 px-4 md:px-0 ">
          <h1 className="text-[28px] font-semibold mt-7 md:ml-3.5">
            Withdraw from Wallet
          </h1>
          <p className="text-base text-grey-950 md:ml-3.5">
            Move your funds safely to your bank account.
          </p>
        </div>

        {/* Step Progress */}
        <StepProgress
          steps={steps}
          activeStep={1}
          completedSteps={[]}
          onStepChange={() => {}}
        />

        {/* Content */}
        <div className="px-4 md:px-0 md:ml-3.5">
          <div className="max-w-[560px] w-full mx-auto mt-8">
            {showCreatePin ? (
              // Create PIN Flow
              <div>
                <div className="bg-[#FAF9FF] p-5 h-[84px]">
                  <p className="text-black italic font-medium mb-[40px]">
                    Create your preferred 4 digit transaction pin to securely
                    perform your transactions
                  </p>
                </div>

                {/* PIN Input Fields */}
                <div className="flex justify-center mt-[64px] mb-[52px] gap-4 ">
                  {newPin.map((digit, index) => (
                    <div key={index} className="relative">
                      <input
                        id={`pin-input-${index}`}
                        type="text"
                        value={digit}
                        onChange={(e) =>
                          handlePinInputChange(index, e.target.value)
                        }
                        className="w-16 h-16 text-center text-2xl font-bold border-2 border-[#D5D7DA] rounded-[50%] focus:border-primary outline-none"
                        maxLength={1}
                      />
                    </div>
                  ))}
                </div>

                {/* Set PIN Button */}
                <button
                  onClick={handleSetPin}
                  disabled={newPin.join("").length !== 4}
                  className={`w-full py-4 px-6 flex gap-2 items-center justify-center rounded-full font-bold text-base transition-all ${
                    newPin.join("").length === 4
                      ? "bg-primary text-white hover:bg-primary/90"
                      : "bg-primary/50 text-white hover:bg-primary/90 cursor-not-allowed"
                  }`}
                >
                  <span>Set Pin</span>
                </button>
              </div>
            ) : showAddAccount ? (
              // Add Account Form
              <div>
                {/* Back to Withdrawal Button */}
                <button
                  onClick={handleBackToSelect}
                  className="flex items-center gap-2 text-primary font-medium mb-8"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.5 15L7.5 10L12.5 5"
                      stroke="#4D55F2"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  Back to Withdrawal
                </button>

                {/* Warning Message */}
                <div className="bg-[#FFF3E6] border border-[#FFE4CC] rounded-lg p-4 mb-6 flex gap-3">
                  <div className="flex-shrink-0">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 9V14"
                        stroke="#FF8C04"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M12 21.41H5.94C2.47 21.41 1.25 18.93 2.92 15.99L5.95 10.5L8.98 5.01C10.66 2.07 13.36 2.07 15.04 5.01L18.07 10.5L21.1 15.99C22.77 18.93 21.54 21.41 18.08 21.41H12Z"
                        stroke="#FF8C04"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M11.995 17H12.005"
                        stroke="#FF8C04"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-[#B45309] font-medium">
                      Add a new bank account to withdraw your funds. Please
                      ensure the account name matches the name on your profile.
                    </p>
                  </div>
                </div>

                {/* Bank Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-grey-50 mb-2">
                    Bank
                  </label>
                  <div className="relative">
                    <select
                      value={bankName}
                      onChange={(e) => setBankName(e.target.value)}
                      className="w-full h-12 px-4 border border-gray-300 rounded-lg text-base font-normal outline-0 focus:border-primary-50 appearance-none bg-white"
                    >
                      <option value="">Select Bank</option>
                      <option value="GTBank">GTBank</option>
                      <option value="Access Bank">Access Bank</option>
                      <option value="First Bank">First Bank</option>
                      <option value="UBA">UBA</option>
                      <option value="Zenith Bank">Zenith Bank</option>
                    </select>
                    <svg
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none"
                      width="12"
                      height="8"
                      viewBox="0 0 12 8"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 1.5L6 6.5L11 1.5"
                        stroke="#6B7280"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>

                {/* Account Number */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-grey-50 mb-2">
                    Account Number
                  </label>
                  <input
                    type="text"
                    value={accountNumber}
                    onChange={(e) => setAccountNumber(e.target.value)}
                    placeholder="**********"
                    className="w-full h-12 px-4 border border-gray-300 rounded-lg text-base font-normal outline-0 focus:border-primary-50"
                  />
                  <p className="text-xs text-grey-250 mt-1">
                    Enter account number to receive funds
                  </p>
                </div>

                {/* Account Name Display */}
                {accountNumber.length >= 10 && (
                  <div className="mb-6">
                    <div className="flex items-center gap-2 text-green-600">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.3333 4L6 11.3333L2.66667 8"
                          stroke="#10B981"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <span className="text-sm font-medium">
                        ADE BOLUWATIFE
                      </span>
                    </div>
                  </div>
                )}

                {/* Continue Button */}
                <button
                  onClick={handleAddAccountSubmit}
                  disabled={
                    !bankName || !accountNumber || accountNumber.length < 10
                  }
                  className={`w-full py-4 px-6 flex gap-2 items-center justify-center rounded-full font-bold text-base transition-all ${
                    bankName && accountNumber && accountNumber.length >= 10
                      ? "bg-primary text-white hover:bg-primary/90"
                      : "bg-grey-150 text-grey-250 cursor-not-allowed"
                  }`}
                >
                  <span>Continue</span>
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M10.4974 18.3327C15.0998 18.3327 18.8307 14.6017 18.8307 9.99935C18.8307 5.39698 15.0998 1.66602 10.4974 1.66602C5.89502 1.66602 2.16406 5.39698 2.16406 9.99935C2.16406 14.6017 5.89502 18.3327 10.4974 18.3327Z"
                      fill="white"
                    />
                    <path
                      d="M13.8609 9.5582L11.3609 7.0582C11.1193 6.81654 10.7193 6.81654 10.4776 7.0582C10.2359 7.29987 10.2359 7.69987 10.4776 7.94154L11.9109 9.37487H7.58594C7.24427 9.37487 6.96094 9.6582 6.96094 9.99987C6.96094 10.3415 7.24427 10.6249 7.58594 10.6249H11.9109L10.4776 12.0582C10.2359 12.2999 10.2359 12.6999 10.4776 12.9415C10.6026 13.0665 10.7609 13.1249 10.9193 13.1249C11.0776 13.1249 11.2359 13.0665 11.3609 12.9415L13.8609 10.4415C14.1026 10.1999 14.1026 9.79987 13.8609 9.5582Z"
                      fill="white"
                    />
                  </svg>
                </button>
              </div>
            ) : (
              // Select Account View
              <div>
                <p className="text-black italic font-medium mb-[40px]">
                  Select the account you would want your funds sent into
                </p>

                <div className="space-y-4 mb-8">
                  {/* First Account Card - Access Bank (SELECTABLE) */}
                  <div
                    onClick={() => setSelectedAccount("access")}
                    className={`relative p-6 border-2 h-[130px] rounded-2xl cursor-pointer bg-[#FBF9FA] transition-all ${
                      selectedAccount === "access"
                        ? "border-primary-50 bg-[#F5F6FE] "
                        : "border-[#FBF9FA] hover:border-primary-50"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <p
                            className={`font-bold text-[20px] ${
                              selectedAccount === "access"
                                ? "text-[#00008C]"
                                : "text-grey-50"
                            }`}
                          >
                            Ade Boluwatife
                          </p>
                          <p className="text-sm text-grey-250">Access Bank</p>
                          <p className="text-sm text-grey-250">**********</p>
                        </div>
                      </div>
                      {selectedAccount === "access" && (
                        <svg
                          className="mt-[-14px]"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="1"
                            y="1"
                            width="22"
                            height="22"
                            rx="11"
                            fill="#343CD8"
                          />
                          <rect
                            x="1"
                            y="1"
                            width="22"
                            height="22"
                            rx="11"
                            stroke="#343CD8"
                            strokeWidth="2"
                          />
                          <path
                            d="M16 9L10.5 14.5L8 12"
                            stroke="white"
                            strokeWidth="1.4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                      <div className="flex-shrink-0 absolute bottom-0 right-0">
                        {selectedAccount === "access" ? (
                          <BankIllustrationSelected />
                        ) : (
                          <BankIllustrationDefault />
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Second Account Card - Gt Bank (SELECTABLE) */}
                  <div
                    onClick={() => setSelectedAccount("gt")}
                    className={`relative p-6 border-2 h-[130px] rounded-2xl cursor-pointer bg-[#FBF9FA] transition-all ${
                      selectedAccount === "gt"
                        ? "border-primary-50  "
                        : "border-[#FBF9FA] hover:border-primary-50"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <p
                            className={`font-bold text-[20px] ${
                              selectedAccount === "gt"
                                ? "text-[#00008C]"
                                : "text-grey-50"
                            }`}
                          >
                            Ade Boluwatife
                          </p>
                          <p className="text-sm text-grey-250">Gt Bank</p>
                          <p className="text-sm text-grey-250">**********</p>
                        </div>
                      </div>
                      {selectedAccount === "gt" && (
                        <svg
                          className="mt-[-14px]"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="1"
                            y="1"
                            width="22"
                            height="22"
                            rx="11"
                            fill="#343CD8"
                          />
                          <rect
                            x="1"
                            y="1"
                            width="22"
                            height="22"
                            rx="11"
                            stroke="#343CD8"
                            strokeWidth="2"
                          />
                          <path
                            d="M16 9L10.5 14.5L8 12"
                            stroke="white"
                            strokeWidth="1.4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                      <div className="flex-shrink-0 absolute bottom-0 right-0">
                        {selectedAccount === "gt" ? (
                          <BankIllustrationSelected />
                        ) : (
                          <BankIllustrationDefault />
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Add New Account Card - NOT SELECTABLE */}
                  <div className="relative p-6 border-2 border-[#A6AAF9] h-[130px] rounded-2xl border-b-0 bg-[#F8F9FF] ">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <p className="font-bold text-grey-50 text-[20px]">
                            Add New Withdrawal Account
                          </p>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddAccount();
                            }}
                            className="text-sm text-[#FFFFFF] font-medium px-3 py-1 rounded-full mt-2 bg-[#7177F5] border  h-[36px]  transition-all w-fit  items-center flex gap-2 min-w-max"
                          >
                            <span>Add new account</span>
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                opacity="0.4"
                                d="M9.9974 18.3327C14.5998 18.3327 18.3307 14.6017 18.3307 9.99935C18.3307 5.39698 14.5998 1.66602 9.9974 1.66602C5.39502 1.66602 1.66406 5.39698 1.66406 9.99935C1.66406 14.6017 5.39502 18.3327 9.9974 18.3327Z"
                                fill="white"
                              />
                              <path
                                d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                                fill="white"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div className="flex-shrink-0 absolute bottom-0 right-0">
                        <BankIllustrationDefault />
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={handleContinue}
                  disabled={
                    !(selectedAccount === "access" || selectedAccount === "gt")
                  }
                  className={`w-full py-4 px-6 flex gap-2 items-center justify-center mt-[60px] rounded-full font-bold text-base transition-all ${
                    selectedAccount === "access" || selectedAccount === "gt"
                      ? "bg-primary  text-white hover:bg-primary/90"
                      : "bg-grey-150 text-grey-250 cursor-not-allowed"
                  }`}
                >
                  <span> Continue</span>
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M10.4974 18.3327C15.0998 18.3327 18.8307 14.6017 18.8307 9.99935C18.8307 5.39698 15.0998 1.66602 10.4974 1.66602C5.89502 1.66602 2.16406 5.39698 2.16406 9.99935C2.16406 14.6017 5.89502 18.3327 10.4974 18.3327Z"
                      fill="white"
                    />
                    <path
                      d="M13.8609 9.5582L11.3609 7.0582C11.1193 6.81654 10.7193 6.81654 10.4776 7.0582C10.2359 7.29987 10.2359 7.69987 10.4776 7.94154L11.9109 9.37487H7.58594C7.24427 9.37487 6.96094 9.6582 6.96094 9.99987C6.96094 10.3415 7.24427 10.6249 7.58594 10.6249H11.9109L10.4776 12.0582C10.2359 12.2999 10.2359 12.6999 10.4776 12.9415C10.6026 13.0665 10.7609 13.1249 10.9193 13.1249C11.0776 13.1249 11.2359 13.0665 11.3609 12.9415L13.8609 10.4415C14.1026 10.1999 14.1026 9.79987 13.8609 9.5582Z"
                      fill="white"
                    />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />

      {/* PIN Success Modal */}
      {showPinSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl  text-center shadow-xl h-[596px] md:w-[450px] w-full">
            {/* Success Icon */}
            <div className="flex justify-center bg-gradient-to-b rounded-t-3xl mb-6 p-8 pt-[60px] from-[#FDEFE9] to-[#FEF7F4] h-[270px]">
              <div className="relative">
                <div className="text-[100px]">🎉</div>
              </div>
            </div>
            <div className="p-8">
              <h2 className="text-[28px] leading-normal font-semibold text-grey-50 mb-2">
                Your Transaction pin has
              </h2>
              <p className="text-[24px] leading-normal mt-[-3px] text-[#808080] mb-2">
                been set successfully
              </p>
              <p className="text-[16px] text-[#808080] mb-6 max-w-[450px] mx-auto">
                You're all set. You can now safely withdraw your funds into your
                account.
              </p>
              {/* Continue Button */}
              <button
                onClick={handlePinSuccessClose}
                className="bg-primary text-white py-3 mt-[30px] px-8 rounded-full font-medium hover:bg-primary/70 transition-colors"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
