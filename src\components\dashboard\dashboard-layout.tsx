import { Outlet } from 'react-router-dom';
import { Sidebar } from './sidebar';
import { Topbar } from './topbar';

export const DashboardLayout = () => {
  return (
    <div className="flex bg-[#FCFCFC]">
      <div className="hidden md:block">
        <Sidebar />
      </div>
      <div className="flex flex-col w-full">
        <div className="hidden md:block">
          <Topbar />
        </div>
        <div className="md:hidden">
          screen barrrr
        </div>
        <div className="md:ml-[270px] mt-44 overflow-hidden md:mr-[20px] md:mt-[30px] max-w-full">
          <Outlet />
        </div>
      </div>
    </div>
  );
};
