import { useEffect, useRef, useState } from 'react';
import { ArrowLeft } from 'iconsax-react';
import { Button } from '../../components/button/button';

interface VerifyEmailProps {
  email: string;
  onVerify: (otp: string) => void;
  onResend: () => void;
  onBack: () => void;
  onClose: () => void;
}

export const VerifyEmail = ({
  email,
  onVerify,
  onResend,
    onBack,
  onClose
}: VerifyEmailProps) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  const handleChange = (index: number, value: string) => {
    if (/^\d*$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value !== '' && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleVerify = () => {
    const otpString = otp.join('');
    if (otpString.length === 6) {
      onVerify(otpString);
    }
  };
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setOtp(digits);

      inputRefs.current[5]?.focus();
    }
  };
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && index > 0 && otp[index] === '') {
      inputRefs.current[index - 1]?.focus();
    }
  };
  return (
    <div
      ref={modalRef}
      className="relative  bg-white rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] w-full p-4 z-50 font-rethink">
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-4.5 tracking-[0.12em] text-primary">
          ACCOUNT EMAIL
        </h3>
        <h2 className="text-2xl  font-medium mb-1.5">Change Email</h2>
        <p className="text-grey-950 text-base">
          Keep your account updated with a new email.
        </p>
      </div>
      {/* Progress indicator */}
      <div className="flex gap-0 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-full"></div>
      </div>
      <button
        onClick={onBack}
        className="rounded-full cursor-pointer my-3 bg-primary/40 h-5 w-5 flex items-center justify-center ">
        <ArrowLeft size="14" color="#4D55F2" />
      </button>
      <p className="text-grey-500 bg-grey-850 italic w-fit px-2.5 py-0.5 rounded-2xl mb-5">
        {email}
      </p>
      <div className="border border-grey-150 px-4 pt-5 pb-4 rounded-xl">
        <p className="text-lg font-normal">
          Kindly enter 6-digit OTP sent to the new email
        </p>

        <div className="flex gap-2 my-5">
          {otp.map((digit, index) => (
            <input
              key={index}
              ref={(el) => {
                if (el) inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              placeholder="0"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              className={`md:w-[56px] md:h-[56px] h-10.5 w-10.5 placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[32px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                digit
                  ? 'text-[#00000D] border-[#DBDDFC]'
                  : 'border-stroke-gray-300'
              } focus:outline-none focus:border-[#A6AAF9] focus:text-primary focus:shadow-[0px_0px_0px_4px_#DBDDFC] `}
            />
          ))}
        </div>
        <div className="flex items-center gap-3 border border-grey-800 py-1 pr-1 pl-3.5 text-nowrap rounded-full  max-w-[218px] text-sm font-medium">
          <span className="text-grey-100 text-sm font-medium leading-none">
            No OTP Yet?
          </span>
          <Button
            variant="neutral"
            onClick={onResend}
            className={`bg-primary-600 h-7 px-3 text-nowrap  items-center text-primary-650 rounded-full `}>
            Resend OTP
          </Button>
        </div>
      </div>

      <button
        onClick={handleVerify}
        className="w-fit bg-primary mt-13 text-white py-2 px-3.5 rounded-full text-sm font-semibold cursor-pointer">
        Change Email
      </button>
    </div>
  );
};
