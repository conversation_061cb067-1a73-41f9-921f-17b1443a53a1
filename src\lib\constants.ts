import {
  Calendar2,
  Chart21,
  Document,
  DollarCircle,
  Gift,
  Global,
  Graph,
  Headphone,
  Home,
  Messages,
  Nebulas,
  Profile2User,
  Setting,
  Timer1,
} from 'iconsax-react';

type NavItem = {
  title: string;
  icon: React.ComponentType<{
    variant?: 'Outline' | 'Bold';
    color?: string;
    className?: string;
  }>;
  path: string;
};

type NavItemsGroup = {
  [key: string]: NavItem[];
};

export const navItems: NavItemsGroup = {
  main: [
    { title: 'Overview', icon: Home, path: '/' },
    { title: 'Insights', icon: Chart21, path: '/insights' },
  ],
  eventBooking: [
    { title: 'Events', icon: Nebulas, path: '/events' },
    { title: 'Bookings', icon: Calendar2, path: '/bookings' },
    { title: 'Messages', icon: Messages, path: '/messages' },
  ],
  tools: [
    { title: 'Budget Planner', icon: Graph, path: '/budget-planner' },
    { title: 'Guest Manager', icon: Profile2User, path: '/guest-manager' },
    { title: 'Website Builder', icon: Global, path: '/website-builder' },
    { title: 'Gift Registry', icon: Gift, path: '/gift-registry' },
  ],
  finance: [
    { title: 'Payments', icon: DollarCircle, path: '/payments' },
    { title: 'Scheduled Payments', icon: Timer1, path: '/scheduled-payments' },
    { title: 'Transactions', icon: Document, path: '/transactions' },
  ],
  others: [
    { title: 'Help Center', icon: Headphone, path: '/help' },
    { title: 'Setting', icon: Setting, path: '/settings' },
  ],
};

export const SOCIAL_LINKS = {
  INSTAGRAM: import.meta.env.VITE_INSTAGRAM_URL,
  TWITTER: import.meta.env.VITE_TWITTER_URL,
  LINKEDIN: import.meta.env.VITE_LINKEDIN_URL,
  TIKTOK: import.meta.env.VITE_TIKTOK_URL,
} as const;
