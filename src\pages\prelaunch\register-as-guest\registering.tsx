import { Calendar, Clock } from 'iconsax-react';
import res from '../../../assets/images/res.png';
import qr from '../../../assets/images/qr-code.png';
import arrow from '../../../assets/images/arrow.png';
import logo from '../../../assets/icons/Ep-Logo.svg';
import { RegisterManually } from './registering-manually';
import { EmailInvite } from './email-invite';
export const Registering = () => {
  const manually = false;

  return (
    <div className=" xl:px-[240px] px-4  font-rethink min-h-screen pb-52 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="py-7.5">
        <img src={logo} alt="logo" />
      </div>
      <div className="flex flex-col mt-20 md:flex-row gap-8 ">
        <div className="  flex-1">
          <h2 className="text-cus-orange-250 font-semibold text-base">
            HI EMMANUEL,
          </h2>

          <div className="mt-3.5">
            <h1 className="text-[56px] font-bold text-dark-blue-400 leading-[110.00000000000001%]">
              You’ve been invited
              <span
                role="img"
                aria-label="kiss emoji"
                // className="inline-block animate-pulse"
              >
                🥳
              </span>
            </h1>
          </div>

          <div className="mt-8 bg-white rounded-[14px] relative">
            <div className="flex items-center gap-4 mb-11  p-4">
              <img src={res} alt="Wedding couple" />
              <div>
                <h3 className="text-2xl font-semibold  italic">
                  Tomi & Bibi's Wedding
                </h3>
                <p className="text-gray-950 text-sm">
                  Bride's groom for bisola's wedding ceremony...
                </p>
              </div>
            </div>

            <div className="absolute -right-3 top-0 text-[32px] italic font-medium leading-[20px] tracking-[-0.02em]">
              📌
            </div>

            <div className="p-4 border-t border-grey-150 flex gap-4">
              <div className="flex items-center gap-1 bg-cus-pink-50 pl-2.5 pr-2 py-0.5 rounded-2xl">
                <Calendar color="#FF5519" size={12} variant="Bulk" />{' '}
                <span className="text-xs italic font-medium text-transparent bg-clip-text bg-gradient-to-b from-[#FF5519] to-[#FF885E]">
                  October 12, 2025
                </span>
              </div>
              <div className="flex items-center gap-1 bg-primary-250 pl-2.5 pr-2 rounded-2xl py-0.5">
                <Clock color="#4D55F2" size={12} variant="Bulk" />{' '}
                <span className="text-primary text-xs italic font-medium">
                  9:30am
                </span>
              </div>
            </div>
          </div>

          <div className="mt-10.5 mb-4 flex gap-4 items-center">
            <div className="flex justify-center">
              <div className="bg-white backdrop-blur-[24px] p-2.5 rounded-[20px] shadow-[0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_rgba(0,0,0,0.06)]">
                <img
                  src={qr}
                  alt="QR Code"
                  className="w-40 h-40 object-cover"
                />
              </div>
            </div>
            <div className="mt-6">
              <img src={arrow} alt="arrow" />
              <p className="inline-block italic ml-3 rotate-2 text-transparent bg-clip-text bg-gradient-to-r from-[#4D55F2] to-[#FF6630] text-base font-brush">
                Scan here to Add <br />
                Event to Calendar
              </p>
            </div>
          </div>
        </div>
        {manually ? <RegisterManually /> : <EmailInvite />}
      </div>
    </div>
  );
};
