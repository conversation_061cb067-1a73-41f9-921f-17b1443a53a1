import { useState, useEffect } from 'react';

interface SendEmailInviteProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
}

export const SendEmailInvite = ({
  onNextStep,
  onFormActiveChange,
}: SendEmailInviteProps) => {
  const [emails, setEmails] = useState<string[]>([
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ]);
  const [inputValue, setInputValue] = useState('');

  // Track form active state
  useEffect(() => {
    onFormActiveChange?.(inputValue !== '');
  }, [inputValue, onFormActiveChange]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      setEmails([...emails, inputValue.trim()]);
      setInputValue('');
    }
  };

  const removeEmail = (index: number) => {
    setEmails(emails.filter((_, i) => i !== index));
  };

  const handleInviteGuests = () => {
    if (onNextStep) {
      onNextStep();
    }
  };

  return (
    <div className="flex-1  pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium ">
        invite guest via email
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-6">
        Got emails for your guests? Send an invite
      </p>

      <div className="">
        <div>
          <label className="block text-sm text-grey-500 font-medium mb-1.5">
            Email
          </label>
          <input
            type="email"
            placeholder="Enter your guest's email"
            className="w-full pr-3.5 py-2.5 pl-2.5 rounded-full text-base text-grey-300 outline-none border border-grey-200"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </div>

        {/* Email tags container */}
        <div className="flex flex-wrap gap-2 mt-5">
          {emails.map((email, index) => (
            <div
              key={index}
              className="bg-gray-100 px-3 py-1 rounded-full flex items-center gap-2">
              <span className="text-grey-500 text-xs md:text-sm font-medium">
                {email}
              </span>
              <button
                onClick={() => removeEmail(index)}
                className="text-grey-500 cursor-pointer">
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-18 py-3.5 border-t border-grey-850 flex justify-end ">
        <button
          onClick={handleInviteGuests}
          className="bg-primary cursor-pointer text-base font-semibold mr-5 text-white h-12 max-w-[135px] w-full rounded-full hover:bg-primary/80 transition-colors">
          Invite Guests
        </button>
      </div>
    </div>
  );
};
