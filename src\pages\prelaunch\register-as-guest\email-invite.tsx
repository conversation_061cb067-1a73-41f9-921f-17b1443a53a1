import { ArrowDown2 } from 'iconsax-react';
import { useState } from 'react';
import { Button } from '../../../components/button/button';
import { Success } from './success';
import { Declined } from './declined';

export const EmailInvite = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [success, setSuccess] = useState(false);
  const [declined, setDeclined] = useState(false);
  const email = '<EMAIL>';
  const acceptInvitation = () => {
    setSuccess(true);
  };
  const declineInvitation = () => {
    setDeclined(true);
  };
  return (
    <>
      <div className="bg-white rounded-2xl p-6 flex-1 font-rethink px-5 pt-7 pb-6 flex flex-col justify-between">
        <div>
          <h2 className="text-2xl font-semibold mb-3 ">Fill in your details</h2>
          <div className="mb-6 text-grey-500 bg-grey-850 w-fit px-2.5 py-0.5 rounded-2xl text-sm font-medium">
            {email}
          </div>
          <div className="mb-6">
            <label
              htmlFor="firstName"
              className="block mb-1.5 text-sm font-medium text-grey-500">
              First name
            </label>
            <input
              type="text"
              id="firstName"
              className="w-full py-2.5 pl-3.5 pr-2.5 rounded-full text-base border border-grey-200 outline-none placeholder:text-grey-300"
              placeholder="Enter your First name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="lastName"
              className="block mb-1.5  text-sm font-medium text-grey-500">
              Last name
            </label>
            <input
              type="text"
              id="lastName"
              className="w-full py-2.5 pl-3.5 pr-2.5 rounded-full text-base border border-grey-200 outline-none placeholder:text-grey-300"
              placeholder="Enter your Last name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
            />
          </div>

          <div className="">
            <label
              htmlFor="mobileNumber"
              className="block mb-1.5  text-sm font-medium text-grey-500">
              Mobile Number
            </label>
            <div className="flex">
              <div className="flex items-center py-2.5 pl-3.5 pr-2.5  rounded-l-full border border-r-0 border-gray-200 bg-white">
                <span className="text-grey-500 font-semibold mr-1 italic text-base">
                  +234
                </span>
                <ArrowDown2 size={16} color="#717680" />
              </div>
              <input
                type="tel"
                id="mobileNumber"
                className="flex-1 py-2.5 pl-3.5 pr-2.5 rounded-r-full border border-gray-200 outline-none"
                placeholder="Enter Mobile Number"
                value={mobileNumber}
                onChange={(e) => setMobileNumber(e.target.value)}
              />
            </div>
          </div>
        </div>

        <div className="mt-20 lg:mt-0">
          <Button onClick={acceptInvitation} type="submit" variant="primary">
            Accept Invitation
          </Button>{' '}
          <Button
            type="submit"
            variant="primary"
            onClick={declineInvitation}
            className="bg-cus-pink-700 mt-4 text-cus-red font-semibold hover:bg-cus-pink-700/90">
            Reject Invitation{' '}
          </Button>{' '}
        </div>
      </div>
      {success && <Success />}
      {declined && <Declined />}
    </>
  );
};
