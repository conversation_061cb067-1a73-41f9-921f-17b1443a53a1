import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

type AuthState = {
  userAppToken: string | null;
  userData: {
    email: string;
    first_name: string;
    last_name: string;
    id: string;
    password_set: boolean;
    profile_picture: string;
  } | null;
  setAuthData: (
    token: string,
    userData: {
      email: string;
      first_name: string;
      last_name: string;
      id: string;
      password_set: boolean;
      profile_picture: string;
    }
  ) => void;
  clearAuthData: () => void;
};

export const useUserAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      userAppToken: null,
      userData: null,
      setAuthData: (token, userData) => set({ userAppToken: token, userData }),
      clearAuthData: () => set({ userAppToken: null, userData: null }),
    }),
    {
      name: 'user-auth-store',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
