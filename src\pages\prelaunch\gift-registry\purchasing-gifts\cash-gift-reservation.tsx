import { ArrowCircleLeft2, Tag2 } from "iconsax-react";
import { <PERSON><PERSON> } from "../../../../components/button/onboardingButton";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { StepProgress } from "../../../../components/step-progress/step-progress";
import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { GifterDetails } from "./gifter-details";
import { CashGiftAmount } from "./cash-gift-amount";

export const CashGiftReservation = () => {
  const { registryId, cashId } = useParams();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const steps = [
    { id: 1, name: "Gifter's details" },
    { id: 2, name: "Gift Reservation" },
    { id: 3, name: "Ending notes" },
  ];

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] pb-36">
      <div className="pt-14 mx-auto  md:max-w-[750px] w-full px-4 md:px-0">
        <Button
          variant="primary"
          size="md"
          className="text-primary-650 bg-white"
          iconLeft={
            <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
          }
          onClick={() =>
            navigate(`/gift-registry/${registryId}/cash/${cashId}`)
          }
        >
          Back to Gift Items
        </Button>
        <div className="mt-5 flex flex-col md:flex-row justify-between bg-white rounded-2xl">
          <div className="p-4 border-r border-grey-150 flex flex-col justify-between">
            <div>
              <img
                src={stackMoney}
                alt="cash-gift"
                className="w-[250px] h-[250px] object-contain"
              />
              <p className="text-[22px]  font-medium text-grey-750 mt-5.5">
                Trip to Zanzibar for honeymoon
              </p>
              <div className="mt-3 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                <Tag2 size={12} variant="Bulk" color="#5856D6" />
                <span className="text-perple-50 text-sm font-bold">
                  ₦1,650,000
                </span>
              </div>
              <div className="md:mt-8 mt-4 flex items-center gap-1.5">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-grey-100">
                  Crowd gifting enabled
                </span>
              </div>
              <p className="text-xs text-grey-100 mt-2">
                ₦850,000 contributed by 4 people
              </p>
            </div>
            <div className="mt-5">
              <p className="text-sm text-grey-100">Total Amount</p>
              <p className="text-base font-medium text-grey-750">
                ₦3,800,000.00
              </p>
            </div>
          </div>
          <div className="flex-1 pt-6">
            <div className="px-5">
              <h2 className="text-[22px] font-medium text-grey-750">
                Reserve cash gift for Olatunde
              </h2>
              <p className="text-base text-grey-100">
                Flawless makeup for your big day.
              </p>
            </div>
            <StepProgress
              steps={steps}
              activeStep={activeStep}
              completedSteps={completedSteps}
              onStepChange={handleStepChange}
            />
            {activeStep === 1 && (
              <GifterDetails
                onContinue={() => {
                  setCompletedSteps((prev) => [...new Set([...prev, 1])]);
                  setActiveStep(2);
                }}
              />
            )}
            {activeStep === 2 && (
              <CashGiftAmount
                onContinue={() => {
                  setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                  setActiveStep(3);
                }}
              />
            )}
            {activeStep === 3 && (
              <div className="pl-5 pr-7">
                <p className="text-base my-4.5">
                  Add a personal note (optional)
                </p>
                <textarea
                  className="w-full h-24 p-3 border border-grey-200 rounded-2xl resize-none outline-none"
                  placeholder="Write a message to the recipient..."
                />
                <Button
                  variant="primary"
                  size="md"
                  className="text-white bg-primary-650 mt-4"
                  iconRight={
                    <ArrowCircleLeft2 size="20" color="#fff" variant="Bulk" />
                  }
                  onClick={() => {
                    // Here you would typically handle the reservation completion
                    // For now, navigate back to the cash gift details
                    navigate(`/gift-registry/${registryId}/cash/${cashId}`);
                  }}
                >
                  Complete Reservation
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
