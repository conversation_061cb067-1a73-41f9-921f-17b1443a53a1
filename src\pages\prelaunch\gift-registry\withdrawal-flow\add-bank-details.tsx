import React, { useState, useEffect } from 'react';
import { ArrowLeft } from 'iconsax-react';
import { useNavigate } from 'react-router-dom';

export const AddBankDetails: React.FC = () => {
  const [bankDetails, setBankDetails] = useState({ bank: '', accountNumber: '' });
  const [isVerified, setIsVerified] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Simulate account verification
    if (bankDetails.bank && bankDetails.accountNumber) {
      setTimeout(() => setIsVerified(true), 1000);
    } else {
      setIsVerified(false);
    }
  }, [bankDetails.bank, bankDetails.accountNumber]);

  const handleContinue = () => {
    if (isVerified) {
      navigate('/withdrawal/authenticate');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto px-4 pt-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button 
            onClick={() => navigate(-1)} 
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <ArrowLeft size="20" color="#666" />
          </button>
          <span className="ml-4 text-gray-600">Back to Withdrawal</span>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-8 px-2">
          <div className="flex items-center">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
              ✓
            </div>
            <span className="ml-2 text-xs text-green-600 font-medium">
              Create Transaction Pin
            </span>
          </div>
          <div className="flex-1 h-px bg-gray-300 mx-3"></div>
          <div className="flex items-center">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
              ✓
            </div>
            <span className="ml-2 text-xs text-green-600 font-medium">
              Select Account
            </span>
          </div>
          <div className="flex-1 h-px bg-gray-300 mx-3"></div>
          <div className="flex items-center">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-medium">
              3
            </div>
            <span className="ml-2 text-xs text-primary font-medium">
              Authenticate
            </span>
          </div>
        </div>

        {/* Content */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            Withdraw from Wallet
          </h1>
          <p className="text-gray-600 mb-6">Add bank details</p>

          {/* Alert */}
          <div className="bg-orange-50 border border-orange-200 rounded-2xl p-4 mb-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold text-sm">E</span>
              </div>
              <p className="text-orange-800 text-sm">
                Add a new bank account to withdraw your funds. Please ensure 
                the account name matches the name on your profile.
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-gray-700 font-medium mb-2">Bank</label>
              <select
                value={bankDetails.bank}
                onChange={(e) => setBankDetails(prev => ({ ...prev, bank: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
              >
                <option value="">Select Bank</option>
                <option value="GTBank">GT Bank</option>
                <option value="Access Bank">Access Bank</option>
                <option value="First Bank">First Bank</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">Account Number</label>
              <input
                type="text"
                value={bankDetails.accountNumber}
                onChange={(e) => setBankDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
                placeholder="**********"
                className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
              />
              {isVerified && (
                <div className="flex items-center gap-2 mt-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-green-600 text-sm font-medium">ADE BOLUWATIFE</span>
                </div>
              )}
            </div>
          </div>

          <button
            onClick={handleContinue}
            disabled={!isVerified}
            className={`w-full py-3 px-4 rounded-full font-medium ${
              isVerified
                ? 'bg-primary text-white hover:bg-primary-600'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};
