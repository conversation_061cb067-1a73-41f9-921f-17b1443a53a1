import { Outlet, useNavigate } from 'react-router-dom';
import { Header } from '../../../components/dashboard/header';
import { Footer } from '../footer';
import { useEffect } from 'react';
import { isTokenValid } from '../../../lib/helpers';
import { useUserAuthStore } from '../../../lib/store/auth';
import { useEventStore } from '../../../lib/store/event';

export const PrelaunchDashboard = () => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents } = useEventStore();
  const { clearAllEventData } = useEventStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        clearAllEventData();
        console.error('Session expired, please login again');
      }
      navigate('/login');
      return;
    }
    if (userEvents.length === 0) {
      navigate('/onboarding');
      return;
    }
  }, [token, navigate, clearAuthData, clearAllEventData, userEvents]);
  if (!token || (token && !isTokenValid(token)) || userEvents.length === 0) {
    return null;
  }
  
  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[561px] mx-auto">
        <Header />
        <Outlet />
      </div>
      <Footer />
    </div>
  );
};
