import { Link, NavLink } from 'react-router-dom';
import logo from '../../assets/icons/Ep-Logo.svg';
import { navItems } from '../../lib/constants';

export const Sidebar = () => {
  return (
    <div className="w-64 h-full bg-grey-350 border-r border-primary-350 fixed pb-20 font-rethink">
      <Link to='/' className="py-6 px-5 border-b border-grey-450">
        <img src={logo} alt="logo" />{' '}
      </Link>
      <div className="pt-3 pb-2 px-4 overflow-y-auto h-full [&::-webkit-scrollbar]:hidden">
        <div className="pt-2">
          <h3 className="tracking-[0.12em] font-medium text-xs text-grey-950">
            MAIN MENU
          </h3>
        </div>
        {navItems.main.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center p-2 mt-1 text-sm text-grey-250 ${
                isActive
                  ? 'bg-primary-650 text-white rounded-[64px] '
                  : 'text-gray-600 hover:bg-primary-650 hover:text-white rounded-[64px] '
              }`
            }>
            {({ isActive }) => {
              const Icon = item.icon;
              return (
                <>
                  <div
                    className={`${
                      isActive
                        ? 'bg-primary'
                        : 'bg-primary-450'
                    } p-1.5 rounded-full`}>
                    <Icon
                      variant="Bold"
                      color={isActive ? '#FFFFFF' : '#B3B3B3'}
                      className="h-5 w-5"
                    />
                  </div>
                  <span className="ml-1.5">{item.title}</span>{' '}
                </>
              );
            }}
          </NavLink>
        ))}

        <div className="pt-4">
          <h3 className="tracking-[0.12em] font-medium text-xs text-grey-950">
            EVENT & BOOKING
          </h3>
        </div>
        {navItems.eventBooking.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center p-2 mt-1 text-sm text-grey-250 ${
                isActive
                  ? 'bg-primary-650 text-white rounded-[64px] '
                  : 'text-gray-600 hover:bg-primary-650 hover:text-white rounded-[64px] '
              }`
            }>
            {/* <div className="p-1.5 bg-primary-450 hover:bg-primary rounded-full">
              {item.icon}
            </div>
            <span className="ml-3">{item.title}</span> */}
            {({ isActive }) => {
              const Icon = item.icon;
              return (
                <>
                  <div
                    className={`${
                      isActive
                        ? 'bg-primary'
                        : 'bg-primary-450'
                    } p-1.5 rounded-full`}>
                    <Icon
                      variant="Bold"
                      color={isActive ? '#FFFFFF' : '#B3B3B3'}
                      className="h-5 w-5"
                    />
                  </div>
                  <span className="ml-1.5">{item.title}</span>{' '}
                </>
              );
            }}
          </NavLink>
        ))}

        <div className="pt-4">
          <h3 className="tracking-[0.12em] font-medium text-xs text-grey-950">
            TOOLS
          </h3>
        </div>
        {navItems.tools.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center p-2 mt-1 text-sm text-grey-250 ${
                isActive
                  ? 'bg-primary-650 text-white rounded-[64px] '
                  : 'text-gray-600 hover:bg-primary-650 hover:text-white rounded-[64px] '
              }`
            }>
            {/* <div className="p-1.5 bg-primary-450 hover:bg-primary rounded-full">
              {item.icon}
            </div>
            <span className="ml-3">{item.title}</span> */}
            {({ isActive }) => {
              const Icon = item.icon;
              return (
                <>
                  <div
                    className={`${
                      isActive
                        ? 'bg-primary'
                        : 'bg-primary-450'
                    } p-1.5 rounded-full`}>
                    <Icon
                      variant="Bold"
                      color={isActive ? '#FFFFFF' : '#B3B3B3'}
                      className="h-5 w-5"
                    />
                  </div>
                  <span className="ml-1.5">{item.title}</span>{' '}
                </>
              );
            }}
          </NavLink>
        ))}

        <div className="pt-4">
          <h3 className="tracking-[0.12em] font-medium text-xs text-grey-950">
            FINANCE
          </h3>
        </div>
        {navItems.finance.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center p-2 mt-1 text-sm text-grey-250 ${
                isActive
                  ? 'bg-primary-650 text-white rounded-[64px] '
                  : 'text-gray-600 hover:bg-primary-650 hover:text-white rounded-[64px] '
              }`
            }>
            {/* <div className="p-1.5 bg-primary-450 hover:bg-primary rounded-full">
              {item.icon}
            </div>
            <span className="ml-3">{item.title}</span> */}
            {({ isActive }) => {
              const Icon = item.icon;
              return (
                <>
                  <div
                    className={`${
                      isActive
                        ? 'bg-primary'
                        : 'bg-primary-450'
                    } p-1.5 rounded-full`}>
                    <Icon
                      variant="Bold"
                      color={isActive ? '#FFFFFF' : '#B3B3B3'}
                      className="h-5 w-5"
                    />
                  </div>
                  <span className="ml-1.5">{item.title}</span>{' '}
                </>
              );
            }}
          </NavLink>
        ))}

        <div className="pt-4">
          <h3 className="tracking-[0.12em] font-medium text-xs text-grey-950">
            OTHERS
          </h3>
        </div>
        {navItems.others.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center p-2 mt-1 text-sm text-grey-250 ${
                isActive
                  ? 'bg-primary-650 text-white rounded-[64px] '
                  : 'text-gray-600 hover:bg-primary-650 hover:text-white rounded-[64px] '
              }`
            }>
            {/* <div className="p-1.5 bg-primary-450 hover:bg-primary rounded-full">
              {item.icon}
            </div>
            <span className="ml-3">{item.title}</span> */}
            {({ isActive }) => {
              const Icon = item.icon;
              return (
                <>
                  <div
                    className={`${
                      isActive
                        ? 'bg-primary'
                        : 'bg-primary-450'
                    } p-1.5 rounded-full`}>
                    <Icon
                      variant="Bold"
                      color={isActive ? '#FFFFFF' : '#B3B3B3'}
                      className="h-5 w-5"
                    />
                  </div>
                  <span className="ml-1.5">{item.title}</span>{' '}
                </>
              );
            }}
          </NavLink>
        ))}
      </div>
    </div>
  );
};
