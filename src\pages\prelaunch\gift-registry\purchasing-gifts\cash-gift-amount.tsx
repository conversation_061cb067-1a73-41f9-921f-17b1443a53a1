import { useState } from "react";
import { ArrowCircleRight2, Moneys } from "iconsax-react";
import { Button } from "../../../../components/button/onboardingButton";
import { PaymentModal } from "./payment-modal";

export const CashGiftAmount = ({ onContinue }: { onContinue?: () => void }) => {
  const [giftAmount, setGiftAmount] = useState("2000");
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove any non-numeric characters except for decimal point
    const value = e.target.value.replace(/[^0-9]/g, "");
    setGiftAmount(value);
  };

  const formatAmount = (amount: string) => {
    if (!amount) return "";
    return new Intl.NumberFormat("en-NG").format(parseInt(amount));
  };

  return (
    <div className="pl-5 pr-7">
      <div className="my-4.5">
        <p className="text-base mb-2">How would you like to gift Olatunde</p>

        {/* Give Cash Equivalent Section */}
        <div className="bg-white border border-dashed border-grey-900 rounded-[14px] p-4 mb-4">
          <div className="flex items-center gap-2 mb-4">
            <Moneys size={20} color="#5856D6" variant="Bulk" />
            <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
              GIVE CASH EQUIVALENT
            </p>
          </div>

          <div className="mb-4">
            <p className="text-2xl font-bold text-grey-750">
              ₦{formatAmount(giftAmount)}.00
            </p>
            <p className="text-xs italic text-grey-950 mt-2">
              Your payment is securely processed through a trusted gateway,
              ensuring a smooth and safe gift contribution.
            </p>
          </div>
        </div>

        {/* Gift Amount Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-grey-500 mb-2">
            Gift Amount
          </label>
          <div className="relative">
            <input
              type="text"
              value={giftAmount}
              onChange={handleAmountChange}
              className="w-full h-12 pl-4 pr-16 border border-grey-200 rounded-full text-base outline-none"
              placeholder="2000"
            />
            <div className="absolute right-4 top-1/2 -translate-y-1/2 text-grey-500">
              NGN
            </div>
          </div>
          <p className="text-xs text-red-500 mt-1">
            Minimum contribution for this is ₦20,000 NGN
          </p>
        </div>

        <Button
          variant="primary"
          size="md"
          className="text-white bg-primary-650"
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }
          onClick={() => setShowPaymentModal(true)}
        >
          Reserve Gift
        </Button>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentModal
          onPayNow={() => {
            // Payment method selection will be handled within the modal
            // After payment method is selected, continue to next step
            setShowPaymentModal(false);
            onContinue?.();
          }}
          onPayLater={() => {
            setShowPaymentModal(false);
            // Handle pay later logic here
            onContinue?.();
          }}
          onClose={() => setShowPaymentModal(false)}
        />
      )}
    </div>
  );
};
