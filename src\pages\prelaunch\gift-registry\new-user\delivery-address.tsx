import { ArrowRight } from 'iconsax-react';
import { useEffect, useState } from 'react';
import { TextInput } from '../../../../components/inputs/text-input/text-input';

interface DeliveryAddressProps {
  onNextStep: (data: {
    state: string;
    city: string;
    address: string;
    shareAddress: boolean;
  }) => void;
  initialData?: {
    state?: string;
    city?: string;
    address?: string;
    shareAddress?: boolean;
  };
}

export const DeliveryAddress = ({
  onNextStep,
  initialData = {},
}: DeliveryAddressProps) => {
  const [state, setState] = useState(initialData.state || '');
  const [city, setCity] = useState(initialData.city || '');
  const [address, setAddress] = useState(initialData.address || '');
  const [shareAddress, setShareAddress] = useState(
    initialData.shareAddress || false
  );

  useEffect(() => {
    document.body.style.overflow = 'hidden';

    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleContinue = () => {
    onNextStep({
      state,
      city,
      address,
      shareAddress,
    });
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <div className="flex items-end">
          <h2 className="md:text-[38px] text-xl font-medium -tracking-[0.03em]">
            Enter address for delivery of <br /> items purchased
          </h2>
          <button
            type="button"
            className="border border-cus-orange-150  cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold">
            <span>Skip</span>
            <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
              <ArrowRight color="#FF6630" size="10" />
            </div>
          </button>
        </div>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-primary-250 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              type="file"
              accept="image/*"
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />

            <svg
              width="66"
              height="68"
              viewBox="0 0 66 68"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.4"
                d="M36.2682 7.73789C37.3689 7.66092 38.3723 8.54162 38.4559 9.73762L40.1168 33.5029C40.3018 36.1512 38.372 38.4306 35.8762 38.6051L8.47538 40.5211L7.42225 25.4524C9.46076 27.1568 12.1392 28.0716 14.9735 27.8015L14.9725 27.8006C17.6612 27.5565 20.015 26.3038 21.702 24.3726L21.701 24.3717C22.4748 23.5881 23.0975 22.6321 23.5542 21.6137C24.3402 19.9692 24.7333 18.0846 24.6163 16.1503L24.5863 15.7628C24.3235 12.9874 23.114 10.5626 21.3193 8.78322L36.2682 7.73789Z"
                fill="#5F66F3"
                stroke="#5F66F3"
              />
              <path
                d="M58.7321 37.5077L59.2858 45.4295C59.5921 49.813 56.4407 53.5891 52.2303 53.8835L49.6939 54.0608C49.4909 51.1562 47.0421 48.9392 44.2521 49.1343C41.4621 49.3294 39.3455 51.8656 39.5485 54.7703L29.403 55.4797C29.2 52.575 26.7511 50.3581 23.9611 50.5532C21.1711 50.7483 19.0545 53.2845 19.2575 56.1892L16.7211 56.3665C12.5107 56.6609 8.86469 53.3602 8.55834 48.9767L8.00469 41.0549L35.9048 39.1039C38.6948 38.9088 40.8114 36.3726 40.6084 33.468L39.3166 14.9836L43.9836 14.6573C45.8098 14.5296 47.5557 15.4424 48.5833 17.0157L53.4721 24.6079L50.2003 24.8367C48.8053 24.9342 47.747 26.2023 47.8485 27.6546L48.4021 35.5765C48.5036 37.0288 49.728 38.1373 51.123 38.0398L58.7321 37.5077Z"
                fill="#5F66F3"
              />
              <path
                opacity="0.4"
                d="M24.7016 61.1157C27.5032 60.9198 29.6091 58.3965 29.4052 55.4798C29.2014 52.563 26.765 50.3573 23.9634 50.5532C21.1618 50.7491 19.0559 53.2725 19.2597 56.1892C19.4636 59.1059 21.9 61.3116 24.7016 61.1157Z"
                fill="#5F66F3"
              />
              <path
                opacity="0.4"
                d="M44.9906 59.6968C47.7922 59.5009 49.8981 56.9776 49.6943 54.0608C49.4904 51.1441 47.054 48.9384 44.2524 49.1343C41.4508 49.3302 39.3449 51.8535 39.5488 54.7703C39.7526 57.687 42.189 59.8927 44.9906 59.6968Z"
                fill="#5F66F3"
              />
              <path
                opacity="0.4"
                d="M58.461 33.6259L58.7323 37.5076L51.1232 38.0397C49.7282 38.1372 48.5038 37.0287 48.4023 35.5764L47.8486 27.6545C47.7471 26.2022 48.8054 24.9341 50.2004 24.8366L53.4723 24.6078L57.6188 31.0577C58.1054 31.8197 58.3964 32.7017 58.461 33.6259Z"
                fill="#5F66F3"
              />
              <path
                d="M20.0995 8.36707C18.1552 6.80477 15.65 5.97161 12.9905 6.21065C10.61 6.43018 8.49337 7.50691 6.87199 9.10626C4.72766 11.2463 3.51955 14.3825 3.82817 17.7043C4.01731 19.6812 4.70451 21.4906 5.80258 22.9794C6.11295 23.4088 6.44867 23.8364 6.83145 24.2077C8.84356 26.3755 11.784 27.6028 14.9236 27.3037C17.4816 27.0717 19.7177 25.8805 21.3225 24.0435C22.0608 23.3019 22.6594 22.3844 23.1002 21.3983C23.9007 19.7237 24.2751 17.7869 24.0859 15.81C23.7939 12.7258 22.285 10.0982 20.0995 8.36707ZM19.0934 15.1244L14.1651 20.829C13.8105 21.2253 13.3669 21.4421 12.885 21.4758C12.4031 21.5095 11.9336 21.3565 11.5273 21.0134L8.78839 18.6577C7.97396 17.9451 7.86197 16.7058 8.54215 15.8622C9.22233 15.0186 10.4126 14.9087 11.227 15.6213L12.5565 16.7754L16.2582 12.4833C16.9655 11.6643 18.1595 11.6073 18.9504 12.348C19.7395 13.0624 19.8008 14.3054 19.0934 15.1244Z"
                fill="#5F66F3"
              />
            </svg>
          </div>
          <div className="mb-6 -mt-8 md:flex gap-4">
            <div className="w-full md:max-w-[247px] mb-6 md:mb-0">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Select State of Residence
              </label>
              <select
                value={state}
                onChange={(e) => setState(e.target.value)}
                className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0">
                <option value="">Select State</option>
                <option value="lagos">Lagos</option>
                <option value="abuja">Abuja</option>
                <option value="rivers">Rivers</option>
                <option value="oyo">Oyo</option>
                <option value="kano">Kano</option>
              </select>
            </div>
            <TextInput
              id="city"
              label="City"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              placeholder="Enter your city"
              className="text-grey-50 font-bold w-full  italic placeholder:font-normal placeholder:text-grey-700"
            />
          </div>

          <div className="mb-6">
            <TextInput
              id="fullAddress"
              label="Full Address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter your full Address"
              className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
            />
          </div>

          <div className="mb-7 py-3 flex items-center gap-2.5 mt-5">
            <div className="relative flex items-center">
              <input
                type="checkbox"
                id="shareAddress"
                checked={shareAddress}
                onChange={(e) => setShareAddress(e.target.checked)}
                className="appearance-none w-5.5 h-5.5 rounded-full border border-primary-650 checked:bg-primary-650 cursor-pointer"
              />
              {shareAddress && (
                <svg
                  className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4.5 8.1L2.4 6L1.5 6.9L4.5 9.9L10.5 3.9L9.6 3L4.5 8.1Z"
                    fill="white"
                  />
                </svg>
              )}
            </div>
            <label
              htmlFor="shareAddress"
              className="md:text-sm text-xs text-grey-300 italic">
              I consent to sharing my address with friends <br /> and family for
              gift delivery.
            </label>
          </div>

          <div className="flex justify-between">
            <button
              onClick={handleContinue}
              disabled={!state || !city || !address}
              className={`bg-primary-650 text-white py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2 ${
                state && city && address ? '' : 'opacity-50 cursor-not-allowed'
              }`}>
              Continue
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
