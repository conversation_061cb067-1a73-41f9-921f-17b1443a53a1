export const EventGalleryDeclinedPage = () => {
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-b from-[#FEF7F4] to-[#F5F6FE] py-8 px-2 relative">
      {/* Background SVG */}
      <img
        src="/gallery/decline-bg.svg"
        alt="decline bg"
        className="absolute top-0 left-0 w-full h-full object-cover opacity-30 pointer-events-none"
      />
      <div className="w-full max-w-5xl flex flex-col gap-8 items-center z-10">
        {/* Card */}
        <div className="relative bg-white rounded-2xl shadow-2xl flex flex-col w-full max-w-xl p-8 gap-8 items-center">
          {/* Large Close Icon */}
          <img
            src="/icons/close-circle.svg"
            alt="declined"
            className="w-20 h-20 mb-2"
          />
          {/* Declined Message */}
          <div className="text-[#00008C] text-3xl font-medium text-center mb-1">
            You just declined your invitation
          </div>
          <div className="text-[#808080] text-base text-center mb-4">
            We'll miss you at the event! Stay connected for updates and future
            celebrations
          </div>
          {/* User Info */}
          <div className="flex flex-col gap-3 w-full max-w-sm mx-auto">
            <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
              <span className="text-[#808080] text-base">Fullname</span>
              <span className="font-bold text-base text-black">
                Adeeko Emmanuel
              </span>
            </div>
            <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
              <span className="text-[#808080] text-base">Email</span>
              <span className="font-bold text-base text-black">
                <EMAIL>
              </span>
            </div>
            <div className="flex justify-between items-center border border-[#F0F0F0] rounded-lg px-4 py-2">
              <span className="text-[#808080] text-base">Mobile Number</span>
              <span className="font-bold text-base text-black">
                +234 7015263711
              </span>
            </div>
          </div>
          {/* Button */}
          <div className="flex justify-center mt-4 w-full">
            <button className="flex items-center gap-2 bg-[#343CD8] hover:bg-[#2329a6] text-white font-semibold rounded-full px-8 py-3 text-base shadow-lg transition focus:outline-none focus:ring-2 focus:ring-[#343cd8] focus:ring-offset-2">
              Check out EventPark
              <img
                src="/icons/check-eventpark.svg"
                alt="check eventpark"
                className="w-5 h-5"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
