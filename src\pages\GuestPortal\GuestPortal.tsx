import { ImageIcon, MapPinIcon, MessageSquareIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "../../components/ui/tabs";

import { EventDetails } from "../../components/EventDetails";
import {
  EventGallery,
  EventGalleryOverlay,
} from "../../components/EventGallery";
import { GalleryConsentModal } from "../../components/GalleryConsentModal";
import { EventGalleryUserCard } from "../../components/EventGalleryUserCard";
import { EventGalleryDeclinedPage } from "../../components/EventGalleryDeclinedPage";

interface InvitationData {
  id: string;
  eventId: string;
  eventTitle: string;
  eventDescription: string;
  hostName: string;
  guestName: string;
  guestEmail?: string;
  guestMobile?: string;
  invitationType: "email_only" | "full_details"; // Determines which flow to show
  status: "pending" | "accepted" | "rejected";
}

export const GuestPortal = () => {
  const { invitationId, eventId } = useParams<{
    invitationId: string;
    eventId?: string;
  }>();

  const [activeTab, setActiveTab] = useState("rsvp");
  const [invitationStatus, setInvitationStatus] = useState<
    "pending" | "accepted" | "rejected"
  >("pending");
  const [userData, setUserData] = useState({
    fullname: "",
    email: "",
    mobile: "",
  });
  const [invitationData, setInvitationData] = useState<InvitationData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [galleryStep, setGalleryStep] = useState<
    "overlay" | "consent" | "usercard" | "declined" | "gallery"
  >("gallery");
  const [showRSVPConsentModal, setShowRSVPConsentModal] = useState(false);
  const [pendingRSVPAction, setPendingRSVPAction] = useState<
    "accept" | "reject" | null
  >(null);

  // Load invitation data on component mount
  useEffect(() => {
    const loadInvitationData = async () => {
      try {
        // Mock data for now - in real app, this would be an API call
        // Simulate different invitation types based on URL parameter
        const isEmailOnlyInvitation = invitationId?.includes("email-only");

        const mockInvitationData: InvitationData = {
          id: invitationId || "mock-invitation-id",
          eventId: eventId || "mock-event-id",
          eventTitle: "Tomi & Bibi's Wedding",
          eventDescription: "Bride's groom for bisola's wedding ceremony...",
          hostName: "Ololade",
          guestName: "Emmanuel",
          guestEmail: isEmailOnlyInvitation
            ? "<EMAIL>"
            : "<EMAIL>",
          guestMobile: isEmailOnlyInvitation ? undefined : "+234 7015263711",
          invitationType: isEmailOnlyInvitation ? "email_only" : "full_details",
          status: "pending",
        };

        setInvitationData(mockInvitationData);
        setInvitationStatus(mockInvitationData.status);

        // Set initial user data based on invitation type
        if (mockInvitationData.invitationType === "full_details") {
          // For full details invitation, populate all fields
          setUserData({
            fullname: mockInvitationData.guestName,
            email: mockInvitationData.guestEmail || "",
            mobile: mockInvitationData.guestMobile || "",
          });
        } else {
          // For email-only invitation, only populate email
          setUserData({
            fullname: "",
            email: mockInvitationData.guestEmail || "",
            mobile: "",
          });
        }

        // Check if guest has already filled details (for email-only invitations)
        const savedUserData = localStorage.getItem(
          `guest-data-${invitationId}`
        );
        if (
          savedUserData &&
          mockInvitationData.invitationType === "email_only"
        ) {
          setUserData(JSON.parse(savedUserData));
        }
      } catch (error) {
        console.error("Failed to load invitation data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (invitationId) {
      loadInvitationData();
    }
  }, [invitationId, eventId]);

  const handleAcceptInvitation = async () => {
    try {
      // In real app, this would be an API call
      setInvitationStatus("accepted");
      localStorage.setItem(`invitation-status-${invitationId}`, "accepted");
      // You could also redirect to a success page or show a success message
    } catch (error) {
      console.error("Failed to accept invitation:", error);
    }
  };

  const handleRejectInvitation = async () => {
    try {
      // In real app, this would be an API call
      setInvitationStatus("rejected");
      localStorage.setItem(`invitation-status-${invitationId}`, "rejected");
      // You could also redirect to a rejection confirmation page
    } catch (error) {
      console.error("Failed to reject invitation:", error);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white flex flex-row justify-center w-full">
        <div className="bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)] w-full min-h-screen relative flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4d55f2] mx-auto mb-4"></div>
            <p className="text-[#666666] text-lg">Loading invitation...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if no invitation data
  if (!invitationData) {
    return (
      <div className=" flex flex-row justify-center w-full">
        <div className="bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)] w-full min-h-screen relative flex items-center justify-center">
          <div className="text-center">
            <p className="text-[#990000] text-lg mb-4">Invitation not found</p>
            <p className="text-[#666666] text-sm">
              This invitation link may be invalid or expired.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const renderTabContent = () => {
    if (activeTab === "rsvp") {
      // For email-only invitations: Show form to fill details
      if (invitationData?.invitationType === "email_only") {
        return (
          <>
            <Card className="w-full max-w-[424px] bg-white rounded-2xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM">
              <CardContent className="p-5">
                <h2 className="font-semibold text-black text-2xl tracking-[-0.72px] leading-6 mb-6">
                  Fill in your Details
                </h2>

                {/* Email display - rounded grey background */}
                <div className="mb-6 px-2 py-1 bg-[#f5f5f5] rounded-[50px] w-fit border border-[#e6e6e6]">
                  <div className="text-sm text-[#414651] font-medium">
                    {invitationData.guestEmail}
                  </div>
                </div>

                {/* Form fields */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#808080]">
                      First name
                    </label>
                    <input
                      type="text"
                      value={userData.fullname.split(" ")[0] || ""}
                      onChange={(e) => {
                        const lastName = userData.fullname
                          .split(" ")
                          .slice(1)
                          .join(" ");
                        setUserData((prev) => ({
                          ...prev,
                          fullname: `${e.target.value} ${lastName}`.trim(),
                        }));
                      }}
                      className="w-full px-4 py-3 rounded-[50px] border border-[#e6e6e6] bg-white text-black placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                      placeholder="Enter your First name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#808080]">
                      Last name
                    </label>
                    <input
                      type="text"
                      value={
                        userData.fullname.split(" ").slice(1).join(" ") || ""
                      }
                      onChange={(e) => {
                        const firstName = userData.fullname.split(" ")[0] || "";
                        setUserData((prev) => ({
                          ...prev,
                          fullname: `${firstName} ${e.target.value}`.trim(),
                        }));
                      }}
                      className="w-full px-4 py-3 rounded-[50px] border border-[#e6e6e6] bg-white text-black placeholder:text-[#999999] focus:border-[#4d55f2] focus:outline-none transition-colors"
                      placeholder="Enter your Last name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#808080]">
                      Mobile Number
                    </label>
                    <div className="flex rounded-[50px] border border-[#e6e6e6] bg-white overflow-hidden focus-within:border-[#4d55f2] transition-colors">
                      <div className="flex items-center px-4 py-3 bg-white border-r border-[#e6e6e6]">
                        <span className="text-sm text-[#666666] font-medium">
                          +234
                        </span>
                        <svg
                          className="w-4 h-4 ml-2 text-[#666666]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      <input
                        type="tel"
                        value={userData.mobile.replace("+234", "").trim()}
                        onChange={(e) =>
                          setUserData((prev) => ({
                            ...prev,
                            mobile: `+234 ${e.target.value}`,
                          }))
                        }
                        className="flex-1 px-4 py-3 bg-white text-black placeholder:text-[#999999] focus:outline-none"
                        placeholder="Enter Mobile Number"
                      />
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col w-full items-start gap-4 mt-8">
                  {invitationStatus === "pending" && (
                    <>
                      <Button
                        onClick={() => {
                          setPendingRSVPAction("accept");
                          setShowRSVPConsentModal(true);
                        }}
                        disabled={
                          !userData.fullname.trim() || !userData.mobile.trim()
                        }
                        className="relative self-stretch w-full h-[56px] px-7 bg-[#4d55f2] rounded-[64px] text-white text-lg leading-7 font-semibold hover:bg-[#3d45d2] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Accept Invitation
                      </Button>
                      <Button
                        onClick={() => {
                          setPendingRSVPAction("reject");
                          setShowRSVPConsentModal(true);
                        }}
                        className="relative self-stretch w-full h-[56px] px-7 bg-[#fff5f5] rounded-[64px] text-[#990000] text-lg leading-7 font-semibold border border-[#990000] hover:bg-[#ffe5e5] transition-colors"
                      >
                        Reject Invitation
                      </Button>
                    </>
                  )}

                  {invitationStatus === "accepted" && (
                    <div className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#e8f5e8] rounded-[64px] text-[#2d5a2d] text-lg leading-7 font-semibold text-center">
                      ✅ Invitation Accepted
                    </div>
                  )}

                  {invitationStatus === "rejected" && (
                    <div className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#fff5f5] rounded-[64px] text-[#990000] text-lg leading-7 font-semibold text-center">
                      ❌ Invitation Rejected
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            <GalleryConsentModal
              open={showRSVPConsentModal}
              onClose={() => setShowRSVPConsentModal(false)}
              onAgree={() => {
                setShowRSVPConsentModal(false);
                if (pendingRSVPAction === "accept") handleAcceptInvitation();
                if (pendingRSVPAction === "reject") handleRejectInvitation();
                setPendingRSVPAction(null);
              }}
              onDisagree={() => {
                setShowRSVPConsentModal(false);
                setPendingRSVPAction(null);
              }}
            />
          </>
        );
      }

      // For full-details invitations: Show preview of details
      return (
        <>
          <Card className="w-full lg:ml-[73px] max-w-[424px] bg-white rounded-2xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM">
            <CardContent className="p-5">
              <h2 className="font-semibold text-black text-2xl tracking-[-0.72px] leading-6 mb-6">
                Preview your Details
              </h2>

              <div className="flex flex-col w-full items-start gap-1.5 py-2 rounded-xl border-[0.8px] border-solid border-[#e6e6e6] [background:linear-gradient(180deg,rgba(250,250,250,1)_0%,rgba(255,255,255,1)_100%)]">
                {/* User details rows */}
                {Object.entries(userData).map(([key, value], index, array) => (
                  <div
                    key={key}
                    className={`flex items-center justify-between px-3.5 py-5 relative self-stretch w-full ${
                      index !== array.length - 1
                        ? "border-b-[0.5px] border-[#f0f0f0]"
                        : ""
                    }`}
                  >
                    <div className="relative w-fit mt-[-0.50px] font-normal text-[#808080] text-base tracking-[0] leading-[18px] whitespace-nowrap capitalize">
                      {key}
                    </div>
                    <div
                      className={`relative ${
                        key === "email" ? "w-fit italic" : "w-[194px]"
                      } mt-[-0.50px] font-bold text-black text-base text-right tracking-[0] leading-[18px] ${
                        key === "email" ? "whitespace-nowrap" : ""
                      }`}
                    >
                      {value}
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col w-full items-start gap-4 mt-8">
                {invitationStatus === "pending" && (
                  <>
                    <Button
                      onClick={() => {
                        setPendingRSVPAction("accept");
                        setShowRSVPConsentModal(true);
                      }}
                      className="relative self-stretch w-full h-[56px] px-7 bg-[#4d55f2] rounded-[64px] text-white text-lg leading-7 font-semibold hover:bg-[#3d45d2] transition-colors"
                    >
                      Accept Invitation
                    </Button>
                    <Button
                      onClick={() => {
                        setPendingRSVPAction("reject");
                        setShowRSVPConsentModal(true);
                      }}
                      className="relative self-stretch w-full h-[56px] px-7 bg-[#fff5f5] rounded-[64px] text-[#990000] text-lg leading-7 font-semibold border border-[#990000] hover:bg-[#ffe5e5] transition-colors"
                    >
                      Reject Invitation
                    </Button>
                  </>
                )}

                {invitationStatus === "accepted" && (
                  <div className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#e8f5e8] rounded-[64px] text-[#2d5a2d] text-lg leading-7 font-semibold text-center">
                    ✅ Invitation Accepted
                  </div>
                )}

                {invitationStatus === "rejected" && (
                  <div className="relative self-stretch w-full px-7 py-3.5 h-[56px] bg-[#fff5f5] rounded-[64px] text-[#990000] text-lg leading-7 font-semibold text-center">
                    ❌ Invitation Rejected
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          <GalleryConsentModal
            open={showRSVPConsentModal}
            onClose={() => setShowRSVPConsentModal(false)}
            onAgree={() => {
              setShowRSVPConsentModal(false);
              if (pendingRSVPAction === "accept") handleAcceptInvitation();
              if (pendingRSVPAction === "reject") handleRejectInvitation();
              setPendingRSVPAction(null);
            }}
            onDisagree={() => {
              setShowRSVPConsentModal(false);
              setPendingRSVPAction(null);
            }}
          />
        </>
      );
    } else if (activeTab === "details") {
      return <EventDetails />;
    } else if (activeTab === "gallery") {
      // Gallery flow wiring
      if (galleryStep === "overlay") {
        return (
          <>
            <EventGallery />
            <EventGalleryOverlay onAccept={() => setGalleryStep("consent")} />
          </>
        );
      }
      if (galleryStep === "consent") {
        return (
          <>
            <EventGallery />
            <GalleryConsentModal
              open={true}
              onClose={() => setGalleryStep("overlay")}
              onAgree={() => setGalleryStep("usercard")}
              onDisagree={() => setGalleryStep("declined")}
            />
          </>
        );
      }
      if (galleryStep === "usercard") {
        return <EventGalleryUserCard />;
      }
      if (galleryStep === "declined") {
        return <EventGalleryDeclinedPage />;
      }
      // Default: show gallery grid
      return <EventGallery />;
    }
  };

  return (
    <div className="min-h-screen w-full bg-white [background:linear-gradient(173deg,rgba(254,247,244,1)_0%,rgba(245,246,254,1)_100%)]">
      {/* Header */}
      <header className="w-full h-20 border-b border-[#f5f6fe]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center h-20">
            <div className="flex items-center gap-1">
              <img className="w-6 h-6" alt="Vector" src="/vector.svg" />
              <div className="font-bold text-xl text-center tracking-[-0.40px] leading-5 whitespace-nowrap">
                <span className="text-[#000073] tracking-[-0.08px]">
                  EventPark
                </span>
                <span className="text-[#ff6630] tracking-[-0.08px]">.</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 md:px-6 lg:px-8 pt-8">
        {/* Greeting */}
        <div className="font-semibold text-[#ff885e] text-base tracking-[1.92px] leading-[17.6px] mb-6">
          HI {invitationData.guestName.toUpperCase()},
        </div>

        {/* Cards Container */}
        <div className="flex flex-col lg:flex-row gap-6 pb-10">
          {/* Left Column */}
          <div className="flex flex-col gap-6 lg:w-1/2">
            {/* Invitation Card */}
            <div className="w-full max-w-[473px] h-[200px] relative">
              <div className="w-full h-[200px] relative">
                <img
                  className="w-[400px] h-[200px] absolute top-0 right-0"
                  alt="Vector"
                  src="/vector-4.svg"
                />
                <img
                  className="w-[400px] h-[200px] absolute top-0 right-0"
                  alt="Vector"
                  src="/vector-3.svg"
                />
                <img
                  className="absolute w-[103px] h-0.5 top-[152px] left-0"
                  alt="Vector"
                  src="/vector-1.svg"
                />
                <img
                  className="absolute w-[52px] h-0.5 top-[105px] left-[50px]"
                  alt="Vector"
                  src="/vector-1.svg"
                />
                <div className="absolute top-7 left-[106px] font-semibold text-[#343cd8] text-[26px] tracking-[-1.04px] leading-[28.6px] whitespace-nowrap">
                  You&apos;ve been invited 🥳
                </div>
                <img
                  className="absolute w-[336px] h-px top-[76px] left-[106px] object-cover"
                  alt="Line"
                  src="/line-12.svg"
                />
                <div className="inline-flex items-center gap-3.5 absolute top-[101px] left-[114px]">
                  <div className="relative w-[77.42px] h-[72px]">
                    <div className="relative w-[81px] h-[75px]">
                      <div className="absolute w-[62px] h-[62px] top-[5px] left-[5px] bg-[#edeefe] rounded-[12.25px] rotate-[-9.53deg]" />
                      <div className="absolute w-[69px] h-[69px] top-[7px] left-3 rounded-[12.25px] border-[3.5px] border-solid border-[#ffffff] bg-[url(/frame-1321314397.svg)] bg-cover bg-[50%_50%]" />
                    </div>
                  </div>
                  <div className="inline-flex flex-col items-start gap-1.5 relative flex-[0_0_auto]">
                    <div className="relative self-stretch mt-[-1.00px] font-semibold italic text-black text-lg tracking-[-0.54px] leading-[18px]">
                      {invitationData.eventTitle}
                    </div>
                    <div className="relative self-stretch h-[39px] font-normal text-[#666666] text-sm tracking-[-0.42px] leading-[18.9px] max-w-[210px]">
                      {invitationData.eventDescription}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs Navigation */}
            <div className="w-full">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="flex gap-3 lg:ml-[73px] bg-white rounded-3xl p-1.5 w-full md:w-[383px]  h-[50px]">
                  <TabsTrigger
                    value="rsvp"
                    className="inline-flex items-center justify-center gap-2 px-3.5 py-2 bg-transparent rounded-[64px] overflow-hidden data-[state=active]:border      data-[state=active]:border-solid shadow-shadow-xs data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8]"
                  >
                    <MessageSquareIcon className="w-5 h-5" />
                    <span className="text-sm leading-5 font-semibold tracking-[0] whitespace-nowrap">
                      RSVP
                    </span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="details"
                    className="inline-flex items-center justify-center gap-2 px-3.5 py-2 rounded-[64px] overflow-hidden data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8]"
                  >
                    <MapPinIcon className="w-5 h-5" />
                    <span className="font-semibold text-xs tracking-[0] leading-5 whitespace-nowrap">
                      Event Details
                    </span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="gallery"
                    className="inline-flex items-center justify-center gap-2 px-3.5 py-2 rounded-[64px] overflow-hidden data-[state=active]:bg-[#4d55f2] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#343cd8]"
                  >
                    <ImageIcon className="w-5 h-5" />
                    <span className="font-semibold text-xs tracking-[0] leading-5 whitespace-nowrap">
                      Event Gallery
                    </span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Tab Content Container */}
            <div className="w-full">{renderTabContent()}</div>
          </div>

          {/* Right Column - Gift Registry */}
          <div className="lg:w-1/2 flex flex-col gap-[98px]">
            <Card className="w-full max-w-[512px] h-[200px] rounded-3xl overflow-hidden shadow-EVENTPARK-SHADOWS-SM [background:linear-gradient(0deg,rgba(77,85,242,1)_0%,rgba(77,85,242,1)_100%)] pt-[0px] px-5 pb-5">
              <CardContent className="relative h-[200px] p-6">
                <div className="absolute top-[23px] left-1 font-medium text-[#ffffffcc] text-sm tracking-[1.40px] leading-[normal]">
                  GIFT REGISTRY
                </div>

                <div className="absolute w-[492px] h-[200px] top-0 left-1">
                  <img
                    className="w-[267px] h-[200px] left-[225px] blur-[2px] object-cover absolute top-0"
                    alt="Rectangle"
                    src="/rectangle-72.png"
                  />

                  <div className="absolute w-[244px] h-[58px] top-[47px] left-0 font-normal text-2xl tracking-[-0.84px] leading-[28.8px]">
                    <span className="font-medium text-[#cbc6ff] tracking-[-0.20px]">
                      Click to view Items in{" "}
                    </span>
                    <span className="font-medium italic text-white tracking-[-0.20px]">
                      {invitationData.hostName}&apos;s{" "}
                      {invitationData.eventTitle}
                    </span>
                  </div>
                </div>

                <div className="w-[116px] absolute top-[146px] left-0 flex items-start rounded-lg">
                  <img
                    className="relative flex-1 grow mt-[-1.00px] mb-[-3.00px] ml-[-2.00px] mr-[-2.00px]"
                    alt="Vendorperk BUTTON"
                    src="/-vendorperk---button-base.svg"
                  />
                </div>
              </CardContent>
            </Card>

            {activeTab === "details" && (
              <div className="flex flex-col gap-4 w-full  max-w-[465px]">
                {/* Map section */}
                <div className="relative rounded-xl border border-[#F0F0F0] overflow-hidden shadow bg-white">
                  <img
                    src="/images/event-map.png"
                    alt="map"
                    className="w-full h-[280px] object-cover"
                  />
                  <div className="absolute top-3 left-4 bg-white  rounded px-3 py-1 flex items-center gap-2">
                    <span className="font-medium text-sm text-[#4D4D4D]">
                      Eko Hotels and Suites, Lagos Nigeria
                    </span>
                  </div>
                  <button
                    className="absolute top-3 right-4 bg-white rounded p-1 shadow"
                    title="Maximize"
                  >
                    <img
                      src="/icons/maximize.svg"
                      alt="maximize"
                      className="w-4 h-4"
                    />
                  </button>
                </div>
                {/* Open address on Google Map */}
                <div className="flex flex-col md:flex-row md:items-center p-2 px-4 shadow-sm w-fit  bg-white rounded-[50px] gap-2 md:gap-4">
                  <span className="font-bold text-sm italic text-[#000059]">
                    Open address on Google Map
                  </span>
                  <a
                    href="https://maps.google.com/?q=Eko+Hotels+and+Suites,+Lagos+Nigeria"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 bg-[#F5F9FF] rounded-[16px] px-4 py-2 text-[#5F66F3] font-semibold text-sm hover:bg-[#e8f0fe] transition"
                  >
                    Open link
                    <img
                      src="/icons/link.svg"
                      alt="link"
                      className="w-4 h-4 opacity-60"
                    />
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};
