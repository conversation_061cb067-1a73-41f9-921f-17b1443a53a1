import { Stepper } from "../../../../components/reuseables/stepper";
import { AccountSetup } from "./account-setup";
import { WalletSetup } from "./wallet-setup";
import { DeliveryAddress } from "./delivery-address";
import { AllSet } from "./all-set";
import { useState } from "react";

interface RegistryData {
  bank?: string;
  accountNumber?: string;
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
}

export const SetupGiftRegistry = ({
  activeStep,
  completedSteps = [],
  onStepChange,
  onClose,
}: {
  activeStep: number;
  completedSteps: number[];
  onStepChange: (step: number) => void;
  onClose?: () => void;
}) => {
  const [registryData, setRegistryData] = useState<RegistryData>({});
  
  const steps = [
    { id: 1, name: 'Account Setup' },
    { id: 2, name: 'Wallet Setup' },
    { id: 3, name: 'Delivery Address' },
    { id: 4, name: 'All Set' },
  ];

  const handleNextStep = (data: Partial<RegistryData>) => {
    setRegistryData((prev) => ({ ...prev, ...data }));
    onStepChange(activeStep + 1);
  };

  const handleComplete = () => {
    // Here you could submit all the collected data to your API
    // For now, we'll just close the modal
    if (onClose) {
      onClose();
    }
  };

  return (
    <div>
      <Stepper
        steps={steps}
        activeStep={activeStep}
        completedSteps={completedSteps}
        title="Create Gift Registry"
        onStepChange={onStepChange}
        onClose={onClose}
      />
      
      {activeStep === 1 && (
        <AccountSetup 
          onNextStep={handleNextStep}
          initialData={registryData}
        />
      )}
      
      {activeStep === 2 && (
        <WalletSetup 
          onNextStep={() => onStepChange(activeStep + 1)}
        />
      )}
      
      {activeStep === 3 && (
        <DeliveryAddress
          onNextStep={handleNextStep}
          initialData={registryData}
        />
      )}

      {activeStep === 4 && (
        <AllSet
          onComplete={handleComplete}
        />
      )}
    </div>
  );
};
